<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ServiceNow Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .response {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: none;
            background: none;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            border-bottom-color: #007bff;
            color: #007bff;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .file-input {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>ServiceNow Integration Test Interface</h1>
    
    <div class="container">
        <div class="tabs">
            <button class="tab active" onclick="showTab('create')">Create Incident</button>
            <button class="tab" onclick="showTab('get')">Get Incidents</button>
            <button class="tab" onclick="showTab('attachments')">Attachments</button>
            <button class="tab" onclick="showTab('camel')">Camel Tests</button>
            <button class="tab" onclick="showTab('health')">Health Check</button>
        </div>

        <!-- Create Incident Tab -->
        <div id="create" class="tab-content active">
            <h2>Create ServiceNow Incident</h2>
            <form id="createIncidentForm">
                <div class="form-group">
                    <label for="shortDescription">Short Description *</label>
                    <input type="text" id="shortDescription" name="shortDescription" required 
                           placeholder="Brief description of the issue">
                </div>
                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea id="description" name="description" rows="4" 
                              placeholder="Detailed description of the issue"></textarea>
                </div>
                <div class="form-group">
                    <label for="category">Category</label>
                    <select id="category" name="category">
                        <option value="">Select Category</option>
                        <option value="Software">Software</option>
                        <option value="Hardware">Hardware</option>
                        <option value="Network">Network</option>
                        <option value="Database">Database</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="urgency">Urgency</label>
                    <select id="urgency" name="urgency">
                        <option value="">Select Urgency</option>
                        <option value="1">1 - High</option>
                        <option value="2">2 - Medium</option>
                        <option value="3">3 - Low</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="impact">Impact</label>
                    <select id="impact" name="impact">
                        <option value="">Select Impact</option>
                        <option value="1">1 - High</option>
                        <option value="2">2 - Medium</option>
                        <option value="3">3 - Low</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="attachments">Attachments</label>
                    <input type="file" id="attachments" name="attachments" multiple class="file-input">
                    <small>Supported: PDF, DOC, DOCX, TXT, JPG, PNG, ZIP, etc. Max 10MB per file</small>
                </div>
                <button type="button" onclick="createIncident()">Create Incident</button>
                <button type="button" onclick="createIncidentWithAttachments()">Create with Attachments</button>
            </form>
            <div id="createResponse" class="response" style="display: none;"></div>
        </div>

        <!-- Get Incidents Tab -->
        <div id="get" class="tab-content">
            <h2>Get ServiceNow Incidents</h2>
            <div class="form-group">
                <label for="incidentId">Incident Sys ID (for single incident)</label>
                <input type="text" id="incidentId" placeholder="Enter ServiceNow sys_id">
                <button type="button" onclick="getIncident()">Get Incident</button>
            </div>
            <div class="form-group">
                <label for="filter">Filter (for multiple incidents)</label>
                <input type="text" id="filter" placeholder="e.g., state=1^category=Software">
                <label for="limit">Limit</label>
                <input type="number" id="limit" value="10" min="1" max="100">
                <button type="button" onclick="getIncidents()">Get Incidents</button>
            </div>
            <div id="getResponse" class="response" style="display: none;"></div>
        </div>

        <!-- Attachments Tab -->
        <div id="attachments" class="tab-content">
            <h2>Incident Attachments</h2>
            <div class="form-group">
                <label for="attachmentIncidentId">Incident Sys ID</label>
                <input type="text" id="attachmentIncidentId" placeholder="Enter ServiceNow sys_id">
                <button type="button" onclick="getAttachments()">Get Attachments</button>
            </div>
            <div class="form-group">
                <label for="newAttachments">Add New Attachments</label>
                <input type="file" id="newAttachments" multiple class="file-input">
                <button type="button" onclick="addAttachments()">Add Attachments</button>
            </div>
            <div id="attachmentResponse" class="response" style="display: none;"></div>
        </div>

        <!-- Camel Tests Tab -->
        <div id="camel" class="tab-content">
            <h2>Camel Integration Tests</h2>
            <div class="form-group">
                <button type="button" onclick="testCamelCreateIncident()">Test Camel Create Incident</button>
                <button type="button" onclick="testCamelGetIncidents()">Test Camel Get Incidents</button>
                <button type="button" onclick="testCamelComponent()">Test Camel Component</button>
                <button type="button" onclick="testWebhook()">Test Webhook</button>
                <button type="button" onclick="testBatchProcess()">Test Batch Process</button>
            </div>
            <div id="camelResponse" class="response" style="display: none;"></div>
        </div>

        <!-- Health Check Tab -->
        <div id="health" class="tab-content">
            <h2>Health Checks</h2>
            <div class="form-group">
                <button type="button" onclick="checkServiceNowHealth()">ServiceNow Health</button>
                <button type="button" onclick="checkCamelHealth()">Camel Health</button>
            </div>
            <div id="healthResponse" class="response" style="display: none;"></div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.classList.remove('active'));
            
            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        function showResponse(elementId, response, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response ${isSuccess ? 'success' : 'error'}`;
            element.textContent = typeof response === 'object' ? JSON.stringify(response, null, 2) : response;
        }

        async function createIncident() {
            const form = document.getElementById('createIncidentForm');
            const formData = new FormData(form);
            
            const incident = {
                short_description: formData.get('shortDescription'),
                description: formData.get('description'),
                category: formData.get('category'),
                urgency: formData.get('urgency'),
                impact: formData.get('impact')
            };

            try {
                const response = await fetch('/api/v1/servicenow/incidents', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(incident)
                });
                
                const result = await response.json();
                showResponse('createResponse', result, response.ok);
            } catch (error) {
                showResponse('createResponse', 'Error: ' + error.message, false);
            }
        }

        async function createIncidentWithAttachments() {
            const form = document.getElementById('createIncidentForm');
            const formData = new FormData(form);

            try {
                const response = await fetch('/api/v1/servicenow/incidents/with-attachments', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                showResponse('createResponse', result, response.ok);
            } catch (error) {
                showResponse('createResponse', 'Error: ' + error.message, false);
            }
        }

        async function getIncident() {
            const incidentId = document.getElementById('incidentId').value;
            if (!incidentId) {
                showResponse('getResponse', 'Please enter an incident sys_id', false);
                return;
            }

            try {
                const response = await fetch(`/api/v1/servicenow/incidents/${incidentId}`);
                const result = await response.json();
                showResponse('getResponse', result, response.ok);
            } catch (error) {
                showResponse('getResponse', 'Error: ' + error.message, false);
            }
        }

        async function getIncidents() {
            const filter = document.getElementById('filter').value;
            const limit = document.getElementById('limit').value;
            
            let url = '/api/v1/servicenow/incidents?';
            if (filter) url += `filter=${encodeURIComponent(filter)}&`;
            if (limit) url += `limit=${limit}`;

            try {
                const response = await fetch(url);
                const result = await response.json();
                showResponse('getResponse', result, response.ok);
            } catch (error) {
                showResponse('getResponse', 'Error: ' + error.message, false);
            }
        }

        async function getAttachments() {
            const incidentId = document.getElementById('attachmentIncidentId').value;
            if (!incidentId) {
                showResponse('attachmentResponse', 'Please enter an incident sys_id', false);
                return;
            }

            try {
                const response = await fetch(`/api/v1/servicenow/incidents/${incidentId}/attachments`);
                const result = await response.json();
                showResponse('attachmentResponse', result, response.ok);
            } catch (error) {
                showResponse('attachmentResponse', 'Error: ' + error.message, false);
            }
        }

        async function addAttachments() {
            const incidentId = document.getElementById('attachmentIncidentId').value;
            const files = document.getElementById('newAttachments').files;
            
            if (!incidentId) {
                showResponse('attachmentResponse', 'Please enter an incident sys_id', false);
                return;
            }
            
            if (files.length === 0) {
                showResponse('attachmentResponse', 'Please select files to upload', false);
                return;
            }

            const formData = new FormData();
            for (let file of files) {
                formData.append('attachments', file);
            }

            try {
                const response = await fetch(`/api/v1/servicenow/incidents/${incidentId}/attachments`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                showResponse('attachmentResponse', result, response.ok);
            } catch (error) {
                showResponse('attachmentResponse', 'Error: ' + error.message, false);
            }
        }

        async function testCamelCreateIncident() {
            try {
                const response = await fetch('/api/v1/servicenow/test/camel/create-incident?shortDescription=Test via Camel&description=Test incident created via Camel route', {
                    method: 'POST'
                });
                const result = await response.text();
                showResponse('camelResponse', result, response.ok);
            } catch (error) {
                showResponse('camelResponse', 'Error: ' + error.message, false);
            }
        }

        async function testCamelGetIncidents() {
            try {
                const response = await fetch('/api/v1/servicenow/test/camel/get-incidents');
                const result = await response.text();
                showResponse('camelResponse', result, response.ok);
            } catch (error) {
                showResponse('camelResponse', 'Error: ' + error.message, false);
            }
        }

        async function testCamelComponent() {
            try {
                const response = await fetch('/api/v1/servicenow/test/camel-component/create-incident?shortDescription=Test via Component&description=Test incident via Camel component', {
                    method: 'POST'
                });
                const result = await response.text();
                showResponse('camelResponse', result, response.ok);
            } catch (error) {
                showResponse('camelResponse', 'Error: ' + error.message, false);
            }
        }

        async function testWebhook() {
            try {
                const response = await fetch('/api/v1/servicenow/test/webhook', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        incident_id: 'test123',
                        state: '2',
                        event: 'incident.updated',
                        timestamp: Date.now()
                    })
                });
                const result = await response.text();
                showResponse('camelResponse', result, response.ok);
            } catch (error) {
                showResponse('camelResponse', 'Error: ' + error.message, false);
            }
        }

        async function testBatchProcess() {
            try {
                const response = await fetch('/api/v1/servicenow/test/batch-process', {
                    method: 'POST'
                });
                const result = await response.text();
                showResponse('camelResponse', result, response.ok);
            } catch (error) {
                showResponse('camelResponse', 'Error: ' + error.message, false);
            }
        }

        async function checkServiceNowHealth() {
            try {
                const response = await fetch('/api/v1/servicenow/health');
                const result = await response.text();
                showResponse('healthResponse', result, response.ok);
            } catch (error) {
                showResponse('healthResponse', 'Error: ' + error.message, false);
            }
        }

        async function checkCamelHealth() {
            try {
                const response = await fetch('/api/v1/servicenow/test/camel/health');
                const result = await response.text();
                showResponse('healthResponse', result, response.ok);
            } catch (error) {
                showResponse('healthResponse', 'Error: ' + error.message, false);
            }
        }
    </script>
</body>
</html>

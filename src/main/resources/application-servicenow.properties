# ServiceNow Configuration
# ServiceNow instance URL (replace with your actual instance)
servicenow.instance.url=https://your-instance.service-now.com
servicenow.api.version=v1
servicenow.base.url=${servicenow.instance.url}/api/now/${servicenow.api.version}

# ServiceNow Authentication
servicenow.username=your-username
servicenow.password=your-password
# Alternative: OAuth2 configuration
servicenow.oauth.client.id=your-client-id
servicenow.oauth.client.secret=your-client-secret
servicenow.oauth.token.url=${servicenow.instance.url}/oauth_token.do

# ServiceNow API Endpoints
servicenow.incident.endpoint=${servicenow.base.url}/table/incident
servicenow.attachment.endpoint=${servicenow.base.url}/attachment/file
servicenow.attachment.metadata.endpoint=${servicenow.base.url}/table/sys_attachment

# ServiceNow Request Configuration
servicenow.request.timeout=30000
servicenow.connection.timeout=10000
servicenow.max.retries=3
servicenow.retry.delay=2000

# ServiceNow Incident Default Values
servicenow.incident.default.caller_id=admin
servicenow.incident.default.category=Software
servicenow.incident.default.subcategory=Application
servicenow.incident.default.urgency=3
servicenow.incident.default.impact=3
servicenow.incident.default.priority=5
servicenow.incident.default.state=1
servicenow.incident.default.assignment_group=IT Support

# File Upload Configuration
servicenow.attachment.max.size=10485760
servicenow.attachment.allowed.types=pdf,doc,docx,txt,jpg,jpeg,png,gif,zip,csv,xlsx,xls
servicenow.attachment.temp.directory=${java.io.tmpdir}/servicenow-attachments

# Camel ServiceNow Component Configuration
camel.component.servicenow.instance-name=${servicenow.instance.url}
camel.component.servicenow.user-name=${servicenow.username}
camel.component.servicenow.password=${servicenow.password}
camel.component.servicenow.oauth-client-id=${servicenow.oauth.client.id}
camel.component.servicenow.oauth-client-secret=${servicenow.oauth.client.secret}
camel.component.servicenow.oauth-token-url=${servicenow.oauth.token.url}

# Camel ServiceNow Route Configuration
camel.servicenow.route.enabled=true
camel.servicenow.route.auto-startup=true
camel.servicenow.route.error.handler.enabled=true
camel.servicenow.route.retry.attempts=3
camel.servicenow.route.retry.delay=2000

# Logging Configuration for ServiceNow
logging.level.com.itcinfotech.windchill.microservices.service.servicenow=DEBUG
logging.level.org.apache.camel.component.servicenow=DEBUG

spring.application.name=windchill.microservices
server.port=8080

#Credentials for basic auth
windchill.basic.auth.user=wcadmin
windchill.basic.auth.password=wcadmin

#Configurations for Azure Blob Storage
azure.localDownloadDir=/path/to/download
azure.endpoint=https://ptccustomermozaue1.blob.core.windows.net/moza01-ue1wmi-esi?sv=2023-08-03&si=moza01-ue1wmi-esi-RITM0255203&sr=c&sig=b%2F37kEnyqLjp2MjTjLPF%2FCe1m0yZOLWTkCHfiD7Jx0g%3D
azure.erpFilesDir=/path/to/erp/files
azure.container=your-container-name

windchill.host=https://PP-2501231811W4.portal.ptc.io:8443/
windchill.token.url=Windchill/protocolAuth/servlet/odata/PTC/GetCSRFToken()
windchill.complaint.create.url=Windchill/protocolAuth/servlet/odata/CEM/CustomerExperiences
windchill.upload.stage1.url=Windchill/protocolAuth/servlet/odata/CEM/CustomerExperiences
# + ('VALID_CEM_ID')/PTC.CEM.UploadStage1Action

windchill.upload.stage3.url=Windchill/protocolAuth/servlet/odata/CEM/CustomerExperiences
# + ('VALID_CEM_ID')/PTC.CEM.UploadStage3Action

#Windchill ESI OData domain configuration v7
windchill.esi.base.url=Windchill/protocolAuth/servlet/odata/ESI

#Windchill ProdMgmt OData domain configuration
windchill.prodmgmt.base.url=Windchill/protocolAuth/servlet/odata/ProdMgmt

#Bearer Token Service Configuration (Optional - set to true to enable)
windchill.bearer.token.enabled=true
windchill.bearer.token.url=https://mozarc-int-pingfed-runtime.cloud.thingworx.com/as/token.oauth2
windchill.client.id=DxP_Mozarc_Microservices
windchill.client.secret=172763e904d5f5f0844ff2a91fb11b48894356df6a8d25d34644936c2c80fd2d
windchill.grant.type=client_credentials
windchill.scope=WINDCHILL

#Spring AOP Configuration - Enable CGLIB proxies for @Scheduled methods
spring.aop.proxy-target-class=true

# Include TIBCO EMS and ServiceNow configuration profiles
spring.profiles.include=tibco,servicenow

#Freshservice API Configuration to create ticket when complaint creation errors out,
# upload the zip archive as well to the ticket.
#freshservice.api.url=https://paaapu.freshdesk.com/api/v2
#freshservice.api.key=********************
freshservice.api.url=https://mozarcmedicalservicedesk-november-test.freshservice.com/api/v2
freshservice.api.key=f9fMMB0qZuiLwj7IaEoT
freshservice.attachments.max.size=15728640
freshservice.attachments.allowed.extensions=.jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.txt,.zip


# H2 Database
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=none

# AWS S3
aws.s3.access-key=********************
aws.s3.secret-key=3aSrD5LyAi/KNhTfgdiBBw7jxPh/MWzlI7gZF+dP
aws.s3.region=sa-east-1
aws.s3.bucketName=wc-itc

# SAP Environment
sap.api.url = https://api.cf.us10-001.hana.ondemand.com/

# Destination API
sap.cloud.destination.name=CLOUDFOUNDRYTRIAL

# Authentication
sap.cloud.security.clientid=sb-na-179f05cd-71a4-46f5-863d-2d97bfac204f!a365959
sap.cloud.security.clientsecret=de4cc695-0eaa-446c-8d6a-0495cf93e4a2$cZzE3wTbtxZvkAUDeCgb5xxt46FGS3cdkvA9bDV3ttM=
sap.cloud.security.url=https://4f04073etrial.authentication.us10.hana.ondemand.com

logging.level.com.sap.cloud.sdk.cloudplatform=DEBUG
logging.level.com.sap.cloud.sdk.cloudplatform.connectivity=DEBUG

logging.level.org.springframework.web.reactive.function.client.ExchangeFunctions=DEBUG

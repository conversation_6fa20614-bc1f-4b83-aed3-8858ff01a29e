package com.itcinfotech.windchill.microservices.config;

import org.apache.camel.CamelContext;
import org.apache.camel.component.servicenow.ServiceNowComponent;
import org.apache.camel.component.servicenow.ServiceNowConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;

/**
 * Configuration class for ServiceNow integration
 */
@Configuration
@Profile("servicenow")
public class ServiceNowConfig {

    private static final Logger logger = LoggerFactory.getLogger(ServiceNowConfig.class);

    @Value("${servicenow.instance.url}")
    private String instanceUrl;

    @Value("${servicenow.username}")
    private String username;

    @Value("${servicenow.password}")
    private String password;

    @Value("${servicenow.oauth.client.id:}")
    private String oauthClientId;

    @Value("${servicenow.oauth.client.secret:}")
    private String oauthClientSecret;

    @Value("${servicenow.oauth.token.url:}")
    private String oauthTokenUrl;

    @Value("${servicenow.request.timeout:30000}")
    private int requestTimeout;

    @Value("${servicenow.connection.timeout:10000}")
    private int connectionTimeout;

    @Value("${servicenow.attachment.temp.directory:${java.io.tmpdir}/servicenow-attachments}")
    private String tempAttachmentDirectory;

    /**
     * Creates a RestTemplate bean configured for ServiceNow API calls
     */
    @Bean("serviceNowRestTemplate")
    public RestTemplate serviceNowRestTemplate() {
        logger.info("Configuring ServiceNow RestTemplate with timeout: {} ms", requestTimeout);

        return new RestTemplateBuilder()
                .setConnectTimeout(Duration.ofMillis(connectionTimeout))
                .setReadTimeout(Duration.ofMillis(requestTimeout))
                .build();
    }

    /**
     * Configures the ServiceNow Camel component
     * Note: This is a basic configuration. The ServiceNow component will be configured
     * dynamically in the routes using URL parameters.
     */
    @Bean
    public ServiceNowComponent serviceNowComponent(CamelContext camelContext) {
        logger.info("Configuring ServiceNow Camel component for instance: {}", instanceUrl);

        try {
            ServiceNowComponent component = new ServiceNowComponent();
            component.setCamelContext(camelContext);

            // Register the component with Camel context
            // Configuration will be done via URL parameters in the routes
            camelContext.addComponent("servicenow", component);

            logger.info("ServiceNow Camel component registered successfully");
            return component;

        } catch (Exception e) {
            logger.error("Failed to configure ServiceNow Camel component", e);
            // Return a basic component
            ServiceNowComponent component = new ServiceNowComponent();
            component.setCamelContext(camelContext);
            camelContext.addComponent("servicenow", component);
            return component;
        }
    }

    /**
     * Creates the temporary directory for ServiceNow attachments if it doesn't exist
     */
    @Bean
    public Path serviceNowTempDirectory() {
        try {
            Path tempDir = Paths.get(tempAttachmentDirectory);
            if (!Files.exists(tempDir)) {
                Files.createDirectories(tempDir);
                logger.info("Created ServiceNow temp directory: {}", tempDir.toAbsolutePath());
            } else {
                logger.info("Using existing ServiceNow temp directory: {}", tempDir.toAbsolutePath());
            }
            return tempDir;
        } catch (Exception e) {
            logger.error("Failed to create ServiceNow temp directory: {}", tempAttachmentDirectory, e);
            // Fallback to system temp directory
            Path fallbackDir = Paths.get(System.getProperty("java.io.tmpdir"), "servicenow-attachments");
            try {
                if (!Files.exists(fallbackDir)) {
                    Files.createDirectories(fallbackDir);
                }
                logger.info("Using fallback temp directory: {}", fallbackDir.toAbsolutePath());
                return fallbackDir;
            } catch (Exception ex) {
                logger.error("Failed to create fallback temp directory", ex);
                throw new RuntimeException("Unable to create temp directory for ServiceNow attachments", ex);
            }
        }
    }

    /**
     * Gets the ServiceNow instance URL
     */
    public String getInstanceUrl() {
        return instanceUrl;
    }

    /**
     * Gets the ServiceNow username
     */
    public String getUsername() {
        return username;
    }

    /**
     * Gets the ServiceNow password
     */
    public String getPassword() {
        return password;
    }

    /**
     * Gets the request timeout
     */
    public int getRequestTimeout() {
        return requestTimeout;
    }

    /**
     * Gets the connection timeout
     */
    public int getConnectionTimeout() {
        return connectionTimeout;
    }

    /**
     * Gets the temp attachment directory
     */
    public String getTempAttachmentDirectory() {
        return tempAttachmentDirectory;
    }
}

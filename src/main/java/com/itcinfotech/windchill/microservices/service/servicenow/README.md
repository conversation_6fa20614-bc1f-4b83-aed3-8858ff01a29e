# ServiceNow Integration with Apache Camel

This module provides comprehensive ServiceNow integration using Apache Camel, enabling incident management operations through both REST APIs and Camel routes.

## Features

### Core Functionality
- **Create Incidents**: Create new incidents in ServiceNow with full field support
- **Create Incidents with Attachments**: Single API call to create incidents with file attachments
- **Read Incidents**: Retrieve individual incidents or lists of incidents with filtering
- **Update Incidents**: Modify existing incidents
- **Attachment Management**: Add, retrieve, and manage incident attachments
- **Health Monitoring**: Check ServiceNow connectivity and service health

### Camel Integration
- **Camel Routes**: Pre-configured routes for ServiceNow operations
- **ServiceNow Component**: Direct integration using Apache Camel ServiceNow component
- **Error Handling**: Robust error handling and retry mechanisms
- **Webhook Processing**: Handle ServiceNow notifications and webhooks
- **Batch Processing**: Support for bulk operations

## Configuration

### Properties File: `application-servicenow.properties`

```properties
# ServiceNow Instance Configuration
servicenow.instance.url=https://your-instance.service-now.com
servicenow.username=your-username
servicenow.password=your-password

# OAuth Configuration (Alternative to basic auth)
servicenow.oauth.client.id=your-client-id
servicenow.oauth.client.secret=your-client-secret

# API Endpoints
servicenow.incident.endpoint=${servicenow.base.url}/table/incident
servicenow.attachment.endpoint=${servicenow.base.url}/attachment/file

# Default Values
servicenow.incident.default.caller_id=admin
servicenow.incident.default.category=Software
servicenow.incident.default.urgency=3
servicenow.incident.default.impact=3
servicenow.incident.default.priority=5

# File Upload Settings
servicenow.attachment.max.size=10485760
servicenow.attachment.allowed.types=pdf,doc,docx,txt,jpg,jpeg,png,gif,zip,csv,xlsx,xls
```

## API Endpoints

### REST API Endpoints

#### Create Incident
```http
POST /api/v1/servicenow/incidents
Content-Type: application/json

{
  "short_description": "System outage",
  "description": "Detailed description of the issue",
  "category": "Software",
  "urgency": "2",
  "impact": "2",
  "priority": "3"
}
```

#### Create Incident with Attachments
```http
POST /api/v1/servicenow/incidents/with-attachments
Content-Type: multipart/form-data

short_description=System outage
description=Detailed description
attachments=@file1.pdf
attachments=@file2.jpg
```

#### Get Incident
```http
GET /api/v1/servicenow/incidents/{sys_id}
```

#### Get Incidents with Filtering
```http
GET /api/v1/servicenow/incidents?filter=state=1^category=Software&limit=10&offset=0
```

#### Get Incident Attachments
```http
GET /api/v1/servicenow/incidents/{sys_id}/attachments
```

#### Add Attachments to Incident
```http
POST /api/v1/servicenow/incidents/{sys_id}/attachments
Content-Type: multipart/form-data

attachments=@file1.pdf
attachments=@file2.jpg
```

#### Update Incident
```http
PUT /api/v1/servicenow/incidents/{sys_id}
Content-Type: application/json

{
  "work_notes": "Updated status",
  "state": "2"
}
```

#### Health Check
```http
GET /api/v1/servicenow/health
```

### Test Endpoints

#### Test Incident Creation via Camel
```http
POST /api/v1/servicenow/test/camel/create-incident?shortDescription=Test&description=Test incident
```

#### Test Getting Incidents via Camel
```http
GET /api/v1/servicenow/test/camel/get-incidents
```

#### Test Camel Component Direct
```http
POST /api/v1/servicenow/test/camel-component/create-incident?shortDescription=Test&description=Test via component
```

#### Test Webhook Processing
```http
POST /api/v1/servicenow/test/webhook
Content-Type: application/json

{
  "incident_id": "test123",
  "state": "2",
  "event": "incident.updated"
}
```

#### Camel Health Check
```http
GET /api/v1/servicenow/test/camel/health
```

## Camel Routes

### Available Routes

1. **servicenow-create-incident**: Creates incidents using ServiceNow service
2. **servicenow-get-incidents**: Retrieves incidents using ServiceNow component
3. **servicenow-get-incident**: Gets specific incident using ServiceNow component
4. **servicenow-camel-create-incident**: Creates incidents using ServiceNow component
5. **servicenow-camel-update-incident**: Updates incidents using ServiceNow component
6. **servicenow-webhook**: Processes ServiceNow webhooks
7. **servicenow-batch-process**: Handles batch operations

### Route Usage Examples

```java
// Create incident via Camel
String response = producerTemplate.requestBody("direct:servicenow-create-incident", requestJson, String.class);

// Get incidents via Camel
String incidents = producerTemplate.requestBody("direct:servicenow-get-incidents", "", String.class);

// Get specific incident
String incident = producerTemplate.requestBodyAndHeader(
    "direct:servicenow-get-incident", "", "incidentId", sysId, String.class);
```

## Data Models

### ServiceNowIncident
Main incident entity with all ServiceNow incident fields including:
- sys_id, number, short_description, description
- caller_id, category, subcategory
- urgency, impact, priority, state
- assignment_group, assigned_to
- timestamps and audit fields

### ServiceNowAttachment
Attachment entity with:
- sys_id, file_name, content_type, size_bytes
- table_name, table_sys_id
- download_link, timestamps

### Request/Response DTOs
- ServiceNowIncidentRequest: For creating/updating incidents
- ServiceNowIncidentResponse: Response with incident data and status
- ServiceNowListResponse: Response for multiple incidents

## Error Handling

### Retry Mechanism
- Configurable retry attempts and delays
- Exponential backoff for transient failures
- Circuit breaker pattern for service protection

### Error Responses
All API responses include:
```json
{
  "status": "success|error",
  "message": "Descriptive message",
  "error": "Error details if applicable",
  "result": "Actual data if successful"
}
```

## File Upload Support

### Supported File Types
- Documents: PDF, DOC, DOCX, TXT, RTF
- Spreadsheets: XLS, XLSX, CSV
- Images: JPG, JPEG, PNG, GIF, BMP, TIFF
- Archives: ZIP
- Media: MP4, AVI, MOV
- Data: XML, JSON, LOG

### File Size Limits
- Maximum file size: 10MB per file
- Multiple files supported per incident
- Automatic file validation and sanitization

## Security

### Authentication
- Basic Authentication (username/password)
- OAuth2 Client Credentials flow
- Configurable authentication method

### File Security
- File type validation
- File size limits
- Filename sanitization
- Temporary file cleanup

## Monitoring and Logging

### Health Checks
- ServiceNow connectivity check
- Camel context health monitoring
- Route status monitoring

### Logging
- Comprehensive logging at DEBUG, INFO, WARN, ERROR levels
- Request/response logging
- Performance metrics
- Error tracking

## Usage Examples

### Java Service Usage
```java
@Autowired
private ServiceNowService serviceNowService;

// Create incident
ServiceNowIncidentRequest request = ServiceNowIncidentRequest.builder()
    .shortDescription("System issue")
    .description("Detailed description")
    .category("Software")
    .urgency("2")
    .build();

ServiceNowIncidentResponse response = serviceNowService.createIncident(request);

// Create incident with attachments
ServiceNowIncidentResponse response = serviceNowService.createIncidentWithAttachments(request);
```

### Camel Route Usage
```java
@Autowired
private ProducerTemplate producerTemplate;

// Send to Camel route
String response = producerTemplate.requestBody("direct:servicenow-create-incident", requestJson, String.class);
```

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Verify username/password or OAuth credentials
   - Check ServiceNow instance URL
   - Ensure user has necessary permissions

2. **File Upload Issues**
   - Check file size limits
   - Verify file type is allowed
   - Ensure temp directory is writable

3. **Camel Route Issues**
   - Check route status in health endpoint
   - Verify Camel context is running
   - Review error logs for specific issues

### Debug Mode
Enable debug logging:
```properties
logging.level.com.itcinfotech.windchill.microservices.service.servicenow=DEBUG
logging.level.org.apache.camel.component.servicenow=DEBUG
```

## Dependencies

### Required Dependencies
- Apache Camel ServiceNow Component
- Spring Boot Web
- Spring Boot WebFlux
- Jackson for JSON processing
- Lombok for boilerplate code reduction

### Maven Dependency
```xml
<dependency>
    <groupId>org.apache.camel</groupId>
    <artifactId>camel-servicenow</artifactId>
    <version>${camel.version}</version>
</dependency>
```

## Contributing

When extending this integration:

1. Follow existing patterns for error handling
2. Add comprehensive logging
3. Include unit tests for new functionality
4. Update documentation
5. Follow ServiceNow API best practices
6. Implement proper security measures

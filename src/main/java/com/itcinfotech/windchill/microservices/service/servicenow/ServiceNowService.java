package com.itcinfotech.windchill.microservices.service.servicenow;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.itcinfotech.windchill.microservices.dto.servicenow.ServiceNowAttachment;
import com.itcinfotech.windchill.microservices.dto.servicenow.ServiceNowIncident;
import com.itcinfotech.windchill.microservices.request.ServiceNowIncidentRequest;
import com.itcinfotech.windchill.microservices.response.ServiceNowIncidentResponse;
import com.itcinfotech.windchill.microservices.response.ServiceNowListResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.*;

/**
 * Service class for ServiceNow operations using REST API
 */
@Service
public class ServiceNowService {

    private static final Logger logger = LoggerFactory.getLogger(ServiceNowService.class);

    @Autowired
    @Qualifier("serviceNowRestTemplate")
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private Path serviceNowTempDirectory;

    @Value("${servicenow.instance.url}")
    private String instanceUrl;

    @Value("${servicenow.username}")
    private String username;

    @Value("${servicenow.password}")
    private String password;

    @Value("${servicenow.incident.endpoint}")
    private String incidentEndpoint;

    @Value("${servicenow.attachment.endpoint}")
    private String attachmentEndpoint;

    @Value("${servicenow.attachment.metadata.endpoint}")
    private String attachmentMetadataEndpoint;

    @Value("${servicenow.incident.default.caller_id:admin}")
    private String defaultCallerId;

    @Value("${servicenow.incident.default.category:Software}")
    private String defaultCategory;

    @Value("${servicenow.incident.default.urgency:3}")
    private String defaultUrgency;

    @Value("${servicenow.incident.default.impact:3}")
    private String defaultImpact;

    @Value("${servicenow.incident.default.priority:5}")
    private String defaultPriority;

    @Value("${servicenow.incident.default.state:1}")
    private String defaultState;

    @Value("${servicenow.incident.default.assignment_group:IT Support}")
    private String defaultAssignmentGroup;

    @Value("${servicenow.max.retries:3}")
    private int maxRetries;

    @Value("${servicenow.retry.delay:2000}")
    private long retryDelay;

    /**
     * Creates HTTP headers with basic authentication
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        
        String auth = username + ":" + password;
        byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes());
        String authHeader = "Basic " + new String(encodedAuth);
        headers.set("Authorization", authHeader);
        
        return headers;
    }

    /**
     * Creates an incident in ServiceNow
     */
    public ServiceNowIncidentResponse createIncident(ServiceNowIncidentRequest request) {
        logger.info("Creating ServiceNow incident with short description: {}", request.getShortDescription());
        
        try {
            // Prepare incident data with defaults
            Map<String, Object> incidentData = prepareIncidentData(request);
            
            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(incidentData, headers);
            
            logger.debug("Sending incident creation request to: {}", incidentEndpoint);
            
            ResponseEntity<String> response = restTemplate.exchange(
                incidentEndpoint,
                HttpMethod.POST,
                entity,
                String.class
            );
            
            if (response.getStatusCode().is2xxSuccessful()) {
                JsonNode responseNode = objectMapper.readTree(response.getBody());
                JsonNode resultNode = responseNode.get("result");

                if (resultNode == null) {
                    logger.error("No result node in ServiceNow response: {}", response.getBody());
                    return ServiceNowIncidentResponse.builder()
                        .status("error")
                        .error("Invalid response format from ServiceNow")
                        .message("No result data in response")
                        .build();
                }

                logger.debug("ServiceNow response result: {}", resultNode.toString());

                ServiceNowIncident incident;
                try {
                    incident = objectMapper.treeToValue(resultNode, ServiceNowIncident.class);
                } catch (Exception e) {
                    logger.error("Failed to deserialize ServiceNow incident response", e);
                    logger.debug("Raw response that failed to deserialize: {}", resultNode.toString());

                    // Create a minimal incident response with available data
                    return ServiceNowIncidentResponse.builder()
                        .status("partial_success")
                        .message("Incident created but response parsing failed")
                        .error("Deserialization error: " + e.getMessage())
                        .sysId(resultNode.has("sys_id") ? resultNode.get("sys_id").asText() : null)
                        .number(resultNode.has("number") ? resultNode.get("number").asText() : null)
                        .build();
                }

                logger.info("Successfully created incident: {} with sys_id: {}",
                           incident.getNumber(), incident.getSysId());

                return ServiceNowIncidentResponse.builder()
                    .result(incident)
                    .status("success")
                    .message("Incident created successfully")
                    .sysId(incident.getSysId())
                    .number(incident.getNumber())
                    .state(incident.getState())
                    .incidentUrl(instanceUrl + "/nav_to.do?uri=incident.do?sys_id=" + incident.getSysId())
                    .createdOn(incident.getSysCreatedOn())
                    .build();
            } else {
                logger.error("Failed to create incident. Status: {}, Response: {}",
                           response.getStatusCode(), response.getBody());
                return ServiceNowIncidentResponse.builder()
                    .status("error")
                    .error("Failed to create incident: " + response.getStatusCode())
                    .message(response.getBody())
                    .build();
            }
            
        } catch (Exception e) {
            logger.error("Error creating ServiceNow incident", e);
            return ServiceNowIncidentResponse.builder()
                .status("error")
                .error("Exception occurred: " + e.getMessage())
                .message("Failed to create incident due to internal error")
                .build();
        }
    }

    /**
     * Creates an incident with attachments in a single operation
     */
    public ServiceNowIncidentResponse createIncidentWithAttachments(ServiceNowIncidentRequest request) {
        logger.info("Creating ServiceNow incident with attachments. Short description: {}", 
                   request.getShortDescription());
        
        // First create the incident
        ServiceNowIncidentResponse incidentResponse = createIncident(request);
        
        if (!"success".equals(incidentResponse.getStatus())) {
            return incidentResponse;
        }
        
        // If there are attachments, add them to the incident
        if (request.getAttachments() != null && !request.getAttachments().isEmpty()) {
            logger.info("Adding {} attachments to incident {}", 
                       request.getAttachments().size(), incidentResponse.getSysId());
            
            List<ServiceNowAttachment> attachments = addAttachmentsToIncident(
                incidentResponse.getSysId(), request.getAttachments());
            
            incidentResponse.setAttachments(attachments);
            incidentResponse.setMessage("Incident created successfully with " + attachments.size() + " attachments");
        }
        
        return incidentResponse;
    }

    /**
     * Adds attachments to an existing incident
     */
    public List<ServiceNowAttachment> addAttachmentsToIncident(String incidentSysId, List<MultipartFile> files) {
        logger.info("Adding {} attachments to incident {}", files.size(), incidentSysId);
        
        List<ServiceNowAttachment> attachments = new ArrayList<>();
        List<File> tempFiles = new ArrayList<>();
        
        try {
            for (MultipartFile file : files) {
                if (file.isEmpty()) {
                    logger.warn("Skipping empty file: {}", file.getOriginalFilename());
                    continue;
                }
                
                // Save file to temp directory
                File tempFile = saveToTempDirectory(file);
                tempFiles.add(tempFile);
                
                // Upload attachment
                ServiceNowAttachment attachment = uploadAttachment(incidentSysId, tempFile, file.getContentType());
                if (attachment != null) {
                    attachments.add(attachment);
                }
            }
            
        } catch (Exception e) {
            logger.error("Error adding attachments to incident {}", incidentSysId, e);
        } finally {
            // Clean up temp files
            cleanupTempFiles(tempFiles);
        }
        
        logger.info("Successfully added {} attachments to incident {}", attachments.size(), incidentSysId);
        return attachments;
    }

    /**
     * Uploads a single attachment to ServiceNow
     */
    private ServiceNowAttachment uploadAttachment(String incidentSysId, File file, String contentType) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            
            String auth = username + ":" + password;
            byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes());
            String authHeader = "Basic " + new String(encodedAuth);
            headers.set("Authorization", authHeader);
            
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("table_name", "incident");
            body.add("table_sys_id", incidentSysId);
            body.add("file", new FileSystemResource(file));
            
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
            
            String uploadUrl = attachmentEndpoint + "?table_name=incident&table_sys_id=" + incidentSysId;
            logger.debug("Uploading attachment to: {}", uploadUrl);
            
            ResponseEntity<String> response = restTemplate.exchange(
                uploadUrl,
                HttpMethod.POST,
                requestEntity,
                String.class
            );
            
            if (response.getStatusCode().is2xxSuccessful()) {
                JsonNode responseNode = objectMapper.readTree(response.getBody());
                JsonNode resultNode = responseNode.get("result");
                
                ServiceNowAttachment attachment = objectMapper.treeToValue(resultNode, ServiceNowAttachment.class);
                logger.info("Successfully uploaded attachment: {} ({})", file.getName(), attachment.getSysId());
                return attachment;
            } else {
                logger.error("Failed to upload attachment {}. Status: {}, Response: {}", 
                           file.getName(), response.getStatusCode(), response.getBody());
            }
            
        } catch (Exception e) {
            logger.error("Error uploading attachment: {}", file.getName(), e);
        }
        
        return null;
    }

    /**
     * Prepares incident data with defaults
     */
    private Map<String, Object> prepareIncidentData(ServiceNowIncidentRequest request) {
        Map<String, Object> data = new HashMap<>();
        
        data.put("short_description", request.getShortDescription());
        data.put("description", request.getDescription());
        data.put("caller_id", request.getCallerId() != null ? request.getCallerId() : defaultCallerId);
        data.put("category", request.getCategory() != null ? request.getCategory() : defaultCategory);
        data.put("subcategory", request.getSubcategory());
        data.put("urgency", request.getUrgency() != null ? request.getUrgency() : defaultUrgency);
        data.put("impact", request.getImpact() != null ? request.getImpact() : defaultImpact);
        data.put("priority", request.getPriority() != null ? request.getPriority() : defaultPriority);
        data.put("state", defaultState);
        data.put("assignment_group", request.getAssignmentGroup() != null ? 
                 request.getAssignmentGroup() : defaultAssignmentGroup);
        data.put("assigned_to", request.getAssignedTo());
        data.put("work_notes", request.getWorkNotes());
        data.put("contact_type", request.getContactType());
        data.put("location", request.getLocation());
        data.put("company", request.getCompany());
        data.put("business_service", request.getBusinessService());
        data.put("cmdb_ci", request.getCmdbCi());
        data.put("correlation_id", request.getCorrelationId());
        data.put("u_source", request.getUSource());
        data.put("u_environment", request.getUEnvironment());
        data.put("u_application", request.getUApplication());
        
        // Remove null values
        data.entrySet().removeIf(entry -> entry.getValue() == null);
        
        return data;
    }

    /**
     * Saves MultipartFile to temp directory
     */
    private File saveToTempDirectory(MultipartFile file) throws IOException {
        String fileName = file.getOriginalFilename();
        if (fileName == null) {
            fileName = "attachment_" + System.currentTimeMillis();
        }
        
        Path tempFile = serviceNowTempDirectory.resolve(fileName);
        Files.copy(file.getInputStream(), tempFile, StandardCopyOption.REPLACE_EXISTING);
        
        logger.debug("Saved file to temp directory: {}", tempFile.toAbsolutePath());
        return tempFile.toFile();
    }

    /**
     * Cleans up temporary files
     */
    private void cleanupTempFiles(List<File> tempFiles) {
        for (File file : tempFiles) {
            try {
                if (file.exists() && file.delete()) {
                    logger.debug("Deleted temp file: {}", file.getAbsolutePath());
                }
            } catch (Exception e) {
                logger.warn("Failed to delete temp file: {}", file.getAbsolutePath(), e);
            }
        }
    }

    /**
     * Gets an incident by sys_id
     */
    public ServiceNowIncidentResponse getIncident(String sysId) {
        logger.info("Retrieving ServiceNow incident with sys_id: {}", sysId);

        try {
            HttpHeaders headers = createHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);

            String url = incidentEndpoint + "/" + sysId;
            logger.debug("Getting incident from: {}", url);

            ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                entity,
                String.class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                JsonNode responseNode = objectMapper.readTree(response.getBody());
                JsonNode resultNode = responseNode.get("result");

                ServiceNowIncident incident = objectMapper.treeToValue(resultNode, ServiceNowIncident.class);

                logger.info("Successfully retrieved incident: {}", incident.getNumber());

                return ServiceNowIncidentResponse.builder()
                    .result(incident)
                    .status("success")
                    .message("Incident retrieved successfully")
                    .sysId(incident.getSysId())
                    .number(incident.getNumber())
                    .state(incident.getState())
                    .incidentUrl(instanceUrl + "/nav_to.do?uri=incident.do?sys_id=" + incident.getSysId())
                    .build();
            } else {
                logger.error("Failed to retrieve incident. Status: {}, Response: {}",
                           response.getStatusCode(), response.getBody());
                return ServiceNowIncidentResponse.builder()
                    .status("error")
                    .error("Failed to retrieve incident: " + response.getStatusCode())
                    .message(response.getBody())
                    .build();
            }

        } catch (Exception e) {
            logger.error("Error retrieving ServiceNow incident: {}", sysId, e);
            return ServiceNowIncidentResponse.builder()
                .status("error")
                .error("Exception occurred: " + e.getMessage())
                .message("Failed to retrieve incident due to internal error")
                .build();
        }
    }

    /**
     * Gets incidents with optional filtering
     */
    public ServiceNowListResponse getIncidents(String filter, Integer limit, Integer offset) {
        logger.info("Retrieving ServiceNow incidents with filter: {}, limit: {}, offset: {}",
                   filter, limit, offset);

        try {
            HttpHeaders headers = createHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);

            StringBuilder urlBuilder = new StringBuilder(incidentEndpoint);
            List<String> params = new ArrayList<>();

            if (filter != null && !filter.isEmpty()) {
                params.add("sysparm_query=" + filter);
            }
            if (limit != null && limit > 0) {
                params.add("sysparm_limit=" + limit);
            }
            if (offset != null && offset > 0) {
                params.add("sysparm_offset=" + offset);
            }

            if (!params.isEmpty()) {
                urlBuilder.append("?").append(String.join("&", params));
            }

            String url = urlBuilder.toString();
            logger.debug("Getting incidents from: {}", url);

            ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                entity,
                String.class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                JsonNode responseNode = objectMapper.readTree(response.getBody());
                JsonNode resultNode = responseNode.get("result");

                List<ServiceNowIncident> incidents = new ArrayList<>();
                if (resultNode.isArray()) {
                    for (JsonNode incidentNode : resultNode) {
                        ServiceNowIncident incident = objectMapper.treeToValue(incidentNode, ServiceNowIncident.class);
                        incidents.add(incident);
                    }
                }

                logger.info("Successfully retrieved {} incidents", incidents.size());

                return ServiceNowListResponse.builder()
                    .result(incidents)
                    .status("success")
                    .message("Incidents retrieved successfully")
                    .totalCount(incidents.size())
                    .build();
            } else {
                logger.error("Failed to retrieve incidents. Status: {}, Response: {}",
                           response.getStatusCode(), response.getBody());
                return ServiceNowListResponse.builder()
                    .status("error")
                    .error("Failed to retrieve incidents: " + response.getStatusCode())
                    .message(response.getBody())
                    .build();
            }

        } catch (Exception e) {
            logger.error("Error retrieving ServiceNow incidents", e);
            return ServiceNowListResponse.builder()
                .status("error")
                .error("Exception occurred: " + e.getMessage())
                .message("Failed to retrieve incidents due to internal error")
                .build();
        }
    }

    /**
     * Gets attachments for an incident
     */
    public List<ServiceNowAttachment> getIncidentAttachments(String incidentSysId) {
        logger.info("Retrieving attachments for incident: {}", incidentSysId);

        try {
            HttpHeaders headers = createHeaders();
            HttpEntity<String> entity = new HttpEntity<>(headers);

            String url = attachmentMetadataEndpoint + "?sysparm_query=table_name=incident^table_sys_id=" + incidentSysId;
            logger.debug("Getting attachments from: {}", url);

            ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                entity,
                String.class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                JsonNode responseNode = objectMapper.readTree(response.getBody());
                JsonNode resultNode = responseNode.get("result");

                List<ServiceNowAttachment> attachments = new ArrayList<>();
                if (resultNode.isArray()) {
                    for (JsonNode attachmentNode : resultNode) {
                        ServiceNowAttachment attachment = objectMapper.treeToValue(attachmentNode, ServiceNowAttachment.class);
                        // Set download link
                        attachment.setDownloadLink(instanceUrl + "/api/now/attachment/" + attachment.getSysId() + "/file");
                        attachments.add(attachment);
                    }
                }

                logger.info("Successfully retrieved {} attachments for incident {}", attachments.size(), incidentSysId);
                return attachments;
            } else {
                logger.error("Failed to retrieve attachments. Status: {}, Response: {}",
                           response.getStatusCode(), response.getBody());
                return new ArrayList<>();
            }

        } catch (Exception e) {
            logger.error("Error retrieving attachments for incident: {}", incidentSysId, e);
            return new ArrayList<>();
        }
    }

    /**
     * Updates an existing incident
     */
    public ServiceNowIncidentResponse updateIncident(String sysId, ServiceNowIncidentRequest request) {
        logger.info("Updating ServiceNow incident: {}", sysId);

        try {
            Map<String, Object> incidentData = prepareIncidentData(request);

            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(incidentData, headers);

            String url = incidentEndpoint + "/" + sysId;
            logger.debug("Updating incident at: {}", url);

            ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.PUT,
                entity,
                String.class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                JsonNode responseNode = objectMapper.readTree(response.getBody());
                JsonNode resultNode = responseNode.get("result");

                ServiceNowIncident incident = objectMapper.treeToValue(resultNode, ServiceNowIncident.class);

                logger.info("Successfully updated incident: {}", incident.getNumber());

                return ServiceNowIncidentResponse.builder()
                    .result(incident)
                    .status("success")
                    .message("Incident updated successfully")
                    .sysId(incident.getSysId())
                    .number(incident.getNumber())
                    .state(incident.getState())
                    .incidentUrl(instanceUrl + "/nav_to.do?uri=incident.do?sys_id=" + incident.getSysId())
                    .updatedOn(incident.getSysUpdatedOn())
                    .build();
            } else {
                logger.error("Failed to update incident. Status: {}, Response: {}",
                           response.getStatusCode(), response.getBody());
                return ServiceNowIncidentResponse.builder()
                    .status("error")
                    .error("Failed to update incident: " + response.getStatusCode())
                    .message(response.getBody())
                    .build();
            }

        } catch (Exception e) {
            logger.error("Error updating ServiceNow incident: {}", sysId, e);
            return ServiceNowIncidentResponse.builder()
                .status("error")
                .error("Exception occurred: " + e.getMessage())
                .message("Failed to update incident due to internal error")
                .build();
        }
    }
}

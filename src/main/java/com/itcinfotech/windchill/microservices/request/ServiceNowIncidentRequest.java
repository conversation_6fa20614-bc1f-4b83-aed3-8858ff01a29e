package com.itcinfotech.windchill.microservices.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * Request DTO for creating ServiceNow incidents
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceNowIncidentRequest {

    @NotBlank(message = "Short description is required")
    @Size(max = 160, message = "Short description must not exceed 160 characters")
    @JsonProperty("short_description")
    private String shortDescription;

    @JsonProperty("description")
    private String description;

    @JsonProperty("caller_id")
    private String callerId;

    @JsonProperty("category")
    private String category;

    @JsonProperty("subcategory")
    private String subcategory;

    @JsonProperty("urgency")
    private String urgency;

    @JsonProperty("impact")
    private String impact;

    @JsonProperty("priority")
    private String priority;

    @JsonProperty("assignment_group")
    private String assignmentGroup;

    @JsonProperty("assigned_to")
    private String assignedTo;

    @JsonProperty("work_notes")
    private String workNotes;

    @JsonProperty("contact_type")
    private String contactType;

    @JsonProperty("location")
    private String location;

    @JsonProperty("company")
    private String company;

    @JsonProperty("business_service")
    private String businessService;

    @JsonProperty("cmdb_ci")
    private String cmdbCi;

    @JsonProperty("correlation_id")
    private String correlationId;

    @JsonProperty("u_source")
    private String uSource;

    @JsonProperty("u_environment")
    private String uEnvironment;

    @JsonProperty("u_application")
    private String uApplication;

    // For file attachments
    private List<MultipartFile> attachments;
}

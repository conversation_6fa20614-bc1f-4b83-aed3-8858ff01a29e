package com.itcinfotech.windchill.microservices.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.itcinfotech.windchill.microservices.request.ServiceNowIncidentRequest;
import com.itcinfotech.windchill.microservices.response.ServiceNowIncidentResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.camel.CamelContext;
import org.apache.camel.ProducerTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Test controller for ServiceNow Camel integration
 */
@RestController
@RequestMapping("/api/v1/servicenow/test")
@Tag(name = "ServiceNow Test", description = "Test endpoints for ServiceNow Camel integration")
public class ServiceNowTestController {

    private static final Logger logger = LoggerFactory.getLogger(ServiceNowTestController.class);

    @Autowired
    private CamelContext camelContext;

    @Autowired
    private ProducerTemplate producerTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * Test endpoint to create an incident via Camel route
     */
    @PostMapping("/camel/create-incident")
    @Operation(summary = "Test incident creation via Camel", 
               description = "Creates a test incident using the ServiceNow Camel route")
    public ResponseEntity<String> testCreateIncidentViaCamel(
            @Parameter(description = "Short description for the test incident")
            @RequestParam(value = "shortDescription", defaultValue = "Test incident via Camel") String shortDescription,
            
            @Parameter(description = "Description for the test incident")
            @RequestParam(value = "description", defaultValue = "This is a test incident created via Camel route") String description) {
        
        logger.info("Testing ServiceNow incident creation via Camel route");
        
        try {
            // Create a test incident request
            ServiceNowIncidentRequest request = ServiceNowIncidentRequest.builder()
                .shortDescription(shortDescription)
                .description(description)
                .category("Software")
                .urgency("3")
                .impact("3")
                .priority("5")
                .uSource("Camel Test")
                .build();
            
            String requestJson = objectMapper.writeValueAsString(request);
            
            // Send to Camel route
            String response = producerTemplate.requestBody("direct:servicenow-create-incident", requestJson, String.class);
            
            logger.info("Camel route response: {}", response);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error testing ServiceNow incident creation via Camel", e);
            return ResponseEntity.internalServerError().body("Error: " + e.getMessage());
        }
    }

    /**
     * Test endpoint to get incidents via Camel route
     */
    @GetMapping("/camel/get-incidents")
    @Operation(summary = "Test getting incidents via Camel", 
               description = "Retrieves incidents using the ServiceNow Camel route")
    public ResponseEntity<String> testGetIncidentsViaCamel() {
        logger.info("Testing ServiceNow incident retrieval via Camel route");
        
        try {
            String response = producerTemplate.requestBody("direct:servicenow-get-incidents", "", String.class);
            
            logger.info("Camel route response: {}", response);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error testing ServiceNow incident retrieval via Camel", e);
            return ResponseEntity.internalServerError().body("Error: " + e.getMessage());
        }
    }

    /**
     * Test endpoint to get a specific incident via Camel route
     */
    @GetMapping("/camel/get-incident/{incidentId}")
    @Operation(summary = "Test getting specific incident via Camel", 
               description = "Retrieves a specific incident using the ServiceNow Camel route")
    public ResponseEntity<String> testGetIncidentViaCamel(
            @Parameter(description = "ServiceNow sys_id of the incident", required = true)
            @PathVariable String incidentId) {
        
        logger.info("Testing ServiceNow incident retrieval via Camel route for incident: {}", incidentId);
        
        try {
            String response = producerTemplate.requestBodyAndHeader(
                "direct:servicenow-get-incident", "", "incidentId", incidentId, String.class);
            
            logger.info("Camel route response: {}", response);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error testing ServiceNow incident retrieval via Camel for incident: {}", incidentId, e);
            return ResponseEntity.internalServerError().body("Error: " + e.getMessage());
        }
    }

    /**
     * Test endpoint to create an incident via Camel ServiceNow component
     */
    @PostMapping("/camel-component/create-incident")
    @Operation(summary = "Test incident creation via Camel ServiceNow component", 
               description = "Creates a test incident using the ServiceNow Camel component directly")
    public ResponseEntity<String> testCreateIncidentViaCamelComponent(
            @Parameter(description = "Short description for the test incident")
            @RequestParam(value = "shortDescription", defaultValue = "Test incident via Camel Component") String shortDescription,
            
            @Parameter(description = "Description for the test incident")
            @RequestParam(value = "description", defaultValue = "This is a test incident created via Camel ServiceNow component") String description) {
        
        logger.info("Testing ServiceNow incident creation via Camel ServiceNow component");
        
        try {
            // Create incident data in the format expected by ServiceNow
            String incidentData = String.format(
                "{\"short_description\":\"%s\",\"description\":\"%s\",\"category\":\"Software\",\"urgency\":\"3\",\"impact\":\"3\",\"priority\":\"5\",\"u_source\":\"Camel Component Test\"}",
                shortDescription, description
            );
            
            // Send to Camel ServiceNow component route
            String response = producerTemplate.requestBody("direct:servicenow-camel-create-incident", incidentData, String.class);
            
            logger.info("Camel ServiceNow component response: {}", response);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error testing ServiceNow incident creation via Camel component", e);
            return ResponseEntity.internalServerError().body("Error: " + e.getMessage());
        }
    }

    /**
     * Test endpoint to update an incident via Camel ServiceNow component
     */
    @PutMapping("/camel-component/update-incident/{incidentId}")
    @Operation(summary = "Test incident update via Camel ServiceNow component", 
               description = "Updates a test incident using the ServiceNow Camel component directly")
    public ResponseEntity<String> testUpdateIncidentViaCamelComponent(
            @Parameter(description = "ServiceNow sys_id of the incident", required = true)
            @PathVariable String incidentId,
            
            @Parameter(description = "Work notes to add")
            @RequestParam(value = "workNotes", defaultValue = "Updated via Camel Component Test") String workNotes,
            
            @Parameter(description = "State to set (1=New, 2=In Progress, 6=Resolved, 7=Closed)")
            @RequestParam(value = "state", defaultValue = "2") String state) {
        
        logger.info("Testing ServiceNow incident update via Camel ServiceNow component for incident: {}", incidentId);
        
        try {
            // Create update data
            String updateData = String.format(
                "{\"work_notes\":\"%s\",\"state\":\"%s\",\"u_source\":\"Camel Component Update Test\"}",
                workNotes, state
            );
            
            // Send to Camel ServiceNow component route
            String response = producerTemplate.requestBodyAndHeader(
                "direct:servicenow-camel-update-incident", updateData, "incidentId", incidentId, String.class);
            
            logger.info("Camel ServiceNow component update response: {}", response);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error testing ServiceNow incident update via Camel component for incident: {}", incidentId, e);
            return ResponseEntity.internalServerError().body("Error: " + e.getMessage());
        }
    }

    /**
     * Test endpoint to simulate a ServiceNow webhook
     */
    @PostMapping("/webhook")
    @Operation(summary = "Test ServiceNow webhook processing", 
               description = "Simulates a ServiceNow webhook and processes it via Camel route")
    public ResponseEntity<String> testServiceNowWebhook(
            @Parameter(description = "Webhook data")
            @RequestBody(required = false) String webhookData) {
        
        logger.info("Testing ServiceNow webhook processing");
        
        try {
            if (webhookData == null || webhookData.isEmpty()) {
                webhookData = "{\"incident_id\":\"test123\",\"state\":\"2\",\"event\":\"incident.updated\",\"timestamp\":\"" + 
                             System.currentTimeMillis() + "\"}";
            }
            
            String response = producerTemplate.requestBody("direct:servicenow-webhook", webhookData, String.class);
            
            logger.info("Webhook processing response: {}", response);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error testing ServiceNow webhook processing", e);
            return ResponseEntity.internalServerError().body("Error: " + e.getMessage());
        }
    }

    /**
     * Test endpoint for batch processing
     */
    @PostMapping("/batch-process")
    @Operation(summary = "Test ServiceNow batch processing", 
               description = "Triggers a batch processing operation via Camel route")
    public ResponseEntity<String> testBatchProcessing() {
        logger.info("Testing ServiceNow batch processing");
        
        try {
            String response = producerTemplate.requestBody("direct:servicenow-batch-process", "start", String.class);
            
            logger.info("Batch processing response: {}", response);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error testing ServiceNow batch processing", e);
            return ResponseEntity.internalServerError().body("Error: " + e.getMessage());
        }
    }

    /**
     * Health check for Camel context and routes
     */
    @GetMapping("/camel/health")
    @Operation(summary = "Camel context health check", 
               description = "Checks the health of the Camel context and ServiceNow routes")
    public ResponseEntity<String> camelHealthCheck() {
        logger.info("Checking Camel context health");
        
        try {
            StringBuilder health = new StringBuilder();
            health.append("Camel Context Status: ").append(camelContext.getStatus()).append("\n");
            health.append("Total Routes: ").append(camelContext.getRoutes().size()).append("\n");
            health.append("ServiceNow Routes: ");
            
            long serviceNowRoutes = camelContext.getRoutes().stream()
                .filter(route -> route.getRouteId().contains("servicenow"))
                .count();
            
            health.append(serviceNowRoutes).append("\n");
            
            // List ServiceNow routes
            camelContext.getRoutes().stream()
                .filter(route -> route.getRouteId().contains("servicenow"))
                .forEach(route -> health.append("- ").append(route.getRouteId())
                    .append(" (").append(route.getStatus()).append(")\n"));
            
            return ResponseEntity.ok(health.toString());
            
        } catch (Exception e) {
            logger.error("Error checking Camel health", e);
            return ResponseEntity.internalServerError().body("Error: " + e.getMessage());
        }
    }
}

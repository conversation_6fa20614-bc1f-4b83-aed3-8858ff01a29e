package com.itcinfotech.windchill.microservices.controller;

import com.itcinfotech.windchill.microservices.dto.servicenow.ServiceNowAttachment;
import com.itcinfotech.windchill.microservices.request.ServiceNowIncidentRequest;
import com.itcinfotech.windchill.microservices.response.ServiceNowIncidentResponse;
import com.itcinfotech.windchill.microservices.response.ServiceNowListResponse;
import com.itcinfotech.windchill.microservices.service.servicenow.ServiceNowService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.util.List;

/**
 * REST Controller for ServiceNow operations
 */
@RestController
@RequestMapping("/api/v1/servicenow")
@Tag(name = "ServiceNow", description = "ServiceNow incident management operations")
public class ServiceNowController {

    private static final Logger logger = LoggerFactory.getLogger(ServiceNowController.class);

    @Autowired
    private ServiceNowService serviceNowService;

    /**
     * Creates a new incident in ServiceNow
     */
    @PostMapping("/incidents")
    @Operation(summary = "Create a new incident", 
               description = "Creates a new incident in ServiceNow with the provided details")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Incident created successfully",
                    content = @Content(schema = @Schema(implementation = ServiceNowIncidentResponse.class))),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ServiceNowIncidentResponse> createIncident(
            @Valid @RequestBody ServiceNowIncidentRequest request) {
        
        logger.info("Creating ServiceNow incident: {}", request.getShortDescription());
        
        try {
            ServiceNowIncidentResponse response = serviceNowService.createIncident(request);
            
            if ("success".equals(response.getStatus())) {
                return ResponseEntity.status(HttpStatus.CREATED).body(response);
            } else {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }
            
        } catch (Exception e) {
            logger.error("Error creating ServiceNow incident", e);
            ServiceNowIncidentResponse errorResponse = ServiceNowIncidentResponse.builder()
                .status("error")
                .error("Internal server error: " + e.getMessage())
                .message("Failed to create incident")
                .build();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Creates a new incident with attachments in a single call
     */
    @PostMapping(value = "/incidents/with-attachments", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "Create incident with attachments", 
               description = "Creates a new incident in ServiceNow with attachments in a single API call")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Incident created successfully with attachments",
                    content = @Content(schema = @Schema(implementation = ServiceNowIncidentResponse.class))),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ServiceNowIncidentResponse> createIncidentWithAttachments(
            @Parameter(description = "Short description of the incident", required = true)
            @RequestParam("short_description") String shortDescription,
            
            @Parameter(description = "Detailed description of the incident")
            @RequestParam(value = "description", required = false) String description,
            
            @Parameter(description = "Caller ID")
            @RequestParam(value = "caller_id", required = false) String callerId,
            
            @Parameter(description = "Category")
            @RequestParam(value = "category", required = false) String category,
            
            @Parameter(description = "Subcategory")
            @RequestParam(value = "subcategory", required = false) String subcategory,
            
            @Parameter(description = "Urgency (1-3)")
            @RequestParam(value = "urgency", required = false) String urgency,
            
            @Parameter(description = "Impact (1-3)")
            @RequestParam(value = "impact", required = false) String impact,
            
            @Parameter(description = "Priority (1-5)")
            @RequestParam(value = "priority", required = false) String priority,
            
            @Parameter(description = "Assignment group")
            @RequestParam(value = "assignment_group", required = false) String assignmentGroup,
            
            @Parameter(description = "Assigned to")
            @RequestParam(value = "assigned_to", required = false) String assignedTo,
            
            @Parameter(description = "Work notes")
            @RequestParam(value = "work_notes", required = false) String workNotes,
            
            @Parameter(description = "Contact type")
            @RequestParam(value = "contact_type", required = false) String contactType,
            
            @Parameter(description = "Location")
            @RequestParam(value = "location", required = false) String location,
            
            @Parameter(description = "Company")
            @RequestParam(value = "company", required = false) String company,
            
            @Parameter(description = "Business service")
            @RequestParam(value = "business_service", required = false) String businessService,
            
            @Parameter(description = "CMDB CI")
            @RequestParam(value = "cmdb_ci", required = false) String cmdbCi,
            
            @Parameter(description = "Correlation ID")
            @RequestParam(value = "correlation_id", required = false) String correlationId,
            
            @Parameter(description = "Source")
            @RequestParam(value = "u_source", required = false) String uSource,
            
            @Parameter(description = "Environment")
            @RequestParam(value = "u_environment", required = false) String uEnvironment,
            
            @Parameter(description = "Application")
            @RequestParam(value = "u_application", required = false) String uApplication,
            
            @Parameter(description = "Attachment files")
            @RequestParam(value = "attachments", required = false) List<MultipartFile> attachments) {
        
        logger.info("Creating ServiceNow incident with attachments: {}", shortDescription);
        
        try {
            // Build the request object
            ServiceNowIncidentRequest request = ServiceNowIncidentRequest.builder()
                .shortDescription(shortDescription)
                .description(description)
                .callerId(callerId)
                .category(category)
                .subcategory(subcategory)
                .urgency(urgency)
                .impact(impact)
                .priority(priority)
                .assignmentGroup(assignmentGroup)
                .assignedTo(assignedTo)
                .workNotes(workNotes)
                .contactType(contactType)
                .location(location)
                .company(company)
                .businessService(businessService)
                .cmdbCi(cmdbCi)
                .correlationId(correlationId)
                .uSource(uSource)
                .uEnvironment(uEnvironment)
                .uApplication(uApplication)
                .attachments(attachments)
                .build();
            
            ServiceNowIncidentResponse response = serviceNowService.createIncidentWithAttachments(request);
            
            if ("success".equals(response.getStatus())) {
                return ResponseEntity.status(HttpStatus.CREATED).body(response);
            } else {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }
            
        } catch (Exception e) {
            logger.error("Error creating ServiceNow incident with attachments", e);
            ServiceNowIncidentResponse errorResponse = ServiceNowIncidentResponse.builder()
                .status("error")
                .error("Internal server error: " + e.getMessage())
                .message("Failed to create incident with attachments")
                .build();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Gets an incident by sys_id
     */
    @GetMapping("/incidents/{sysId}")
    @Operation(summary = "Get incident by sys_id", 
               description = "Retrieves a specific incident from ServiceNow by its sys_id")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Incident retrieved successfully",
                    content = @Content(schema = @Schema(implementation = ServiceNowIncidentResponse.class))),
        @ApiResponse(responseCode = "404", description = "Incident not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ServiceNowIncidentResponse> getIncident(
            @Parameter(description = "ServiceNow sys_id of the incident", required = true)
            @PathVariable String sysId) {
        
        logger.info("Retrieving ServiceNow incident: {}", sysId);
        
        try {
            ServiceNowIncidentResponse response = serviceNowService.getIncident(sysId);
            
            if ("success".equals(response.getStatus())) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
            
        } catch (Exception e) {
            logger.error("Error retrieving ServiceNow incident: {}", sysId, e);
            ServiceNowIncidentResponse errorResponse = ServiceNowIncidentResponse.builder()
                .status("error")
                .error("Internal server error: " + e.getMessage())
                .message("Failed to retrieve incident")
                .build();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Gets incidents with optional filtering
     */
    @GetMapping("/incidents")
    @Operation(summary = "Get incidents", 
               description = "Retrieves incidents from ServiceNow with optional filtering")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Incidents retrieved successfully",
                    content = @Content(schema = @Schema(implementation = ServiceNowListResponse.class))),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ServiceNowListResponse> getIncidents(
            @Parameter(description = "ServiceNow query filter (e.g., 'state=1^category=Software')")
            @RequestParam(value = "filter", required = false) String filter,
            
            @Parameter(description = "Maximum number of records to return")
            @RequestParam(value = "limit", required = false, defaultValue = "100") Integer limit,
            
            @Parameter(description = "Number of records to skip")
            @RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset) {
        
        logger.info("Retrieving ServiceNow incidents with filter: {}, limit: {}, offset: {}", 
                   filter, limit, offset);
        
        try {
            ServiceNowListResponse response = serviceNowService.getIncidents(filter, limit, offset);
            
            if ("success".equals(response.getStatus())) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
            }
            
        } catch (Exception e) {
            logger.error("Error retrieving ServiceNow incidents", e);
            ServiceNowListResponse errorResponse = ServiceNowListResponse.builder()
                .status("error")
                .error("Internal server error: " + e.getMessage())
                .message("Failed to retrieve incidents")
                .build();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Gets attachments for an incident
     */
    @GetMapping("/incidents/{sysId}/attachments")
    @Operation(summary = "Get incident attachments",
               description = "Retrieves all attachments for a specific incident")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Attachments retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Incident not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<List<ServiceNowAttachment>> getIncidentAttachments(
            @Parameter(description = "ServiceNow sys_id of the incident", required = true)
            @PathVariable String sysId) {

        logger.info("Retrieving attachments for ServiceNow incident: {}", sysId);

        try {
            List<ServiceNowAttachment> attachments = serviceNowService.getIncidentAttachments(sysId);
            return ResponseEntity.ok(attachments);

        } catch (Exception e) {
            logger.error("Error retrieving attachments for incident: {}", sysId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    /**
     * Adds attachments to an existing incident
     */
    @PostMapping(value = "/incidents/{sysId}/attachments", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "Add attachments to incident",
               description = "Adds one or more attachments to an existing incident")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Attachments added successfully"),
        @ApiResponse(responseCode = "404", description = "Incident not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<List<ServiceNowAttachment>> addAttachmentsToIncident(
            @Parameter(description = "ServiceNow sys_id of the incident", required = true)
            @PathVariable String sysId,

            @Parameter(description = "Attachment files", required = true)
            @RequestParam("attachments") List<MultipartFile> attachments) {

        logger.info("Adding {} attachments to ServiceNow incident: {}", attachments.size(), sysId);

        try {
            List<ServiceNowAttachment> uploadedAttachments =
                serviceNowService.addAttachmentsToIncident(sysId, attachments);

            return ResponseEntity.ok(uploadedAttachments);

        } catch (Exception e) {
            logger.error("Error adding attachments to incident: {}", sysId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    /**
     * Updates an existing incident
     */
    @PutMapping("/incidents/{sysId}")
    @Operation(summary = "Update incident",
               description = "Updates an existing incident in ServiceNow")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Incident updated successfully",
                    content = @Content(schema = @Schema(implementation = ServiceNowIncidentResponse.class))),
        @ApiResponse(responseCode = "404", description = "Incident not found"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<ServiceNowIncidentResponse> updateIncident(
            @Parameter(description = "ServiceNow sys_id of the incident", required = true)
            @PathVariable String sysId,

            @Valid @RequestBody ServiceNowIncidentRequest request) {

        logger.info("Updating ServiceNow incident: {}", sysId);

        try {
            ServiceNowIncidentResponse response = serviceNowService.updateIncident(sysId, request);

            if ("success".equals(response.getStatus())) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

        } catch (Exception e) {
            logger.error("Error updating ServiceNow incident: {}", sysId, e);
            ServiceNowIncidentResponse errorResponse = ServiceNowIncidentResponse.builder()
                .status("error")
                .error("Internal server error: " + e.getMessage())
                .message("Failed to update incident")
                .build();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Health check endpoint for ServiceNow connectivity
     */
    @GetMapping("/health")
    @Operation(summary = "ServiceNow health check",
               description = "Checks connectivity to ServiceNow instance")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "ServiceNow is accessible"),
        @ApiResponse(responseCode = "503", description = "ServiceNow is not accessible")
    })
    public ResponseEntity<String> healthCheck() {
        logger.info("Performing ServiceNow health check");

        try {
            // Try to get a limited number of incidents to test connectivity
            ServiceNowListResponse response = serviceNowService.getIncidents(null, 1, 0);

            if ("success".equals(response.getStatus())) {
                return ResponseEntity.ok("ServiceNow is accessible");
            } else {
                return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body("ServiceNow connectivity issue: " + response.getError());
            }

        } catch (Exception e) {
            logger.error("ServiceNow health check failed", e);
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                .body("ServiceNow health check failed: " + e.getMessage());
        }
    }
}

package com.itcinfotech.windchill.microservices.routes;

import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.model.RouteDefinition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Base abstract class for all Camel routes in the application.
 * Provides common functionality and error handling.
 */
public abstract class BaseRoute extends RouteBuilder {
    
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    
    /**
     * Configure the route with standard error handling
     * 
     * @param routeId The unique identifier for this route
     * @param from The endpoint URI to consume from
     * @return A route definition with error handling configured
     */
    protected RouteDefinition fromWithErrorHandling(String routeId, String from) {
        RouteDefinition route = from(from).routeId(routeId);
        return RouteConfiguration.configureErrorHandling(route);
    }
    
    /**
     * Log route startup information
     */
    @Override
    public void configure() throws Exception {
        logger.info("Configuring route: {}", getClass().getSimpleName());
        configureRoute();
        logger.info("Route configured: {}", getClass().getSimpleName());
    }
    
    /**
     * Abstract method to be implemented by concrete route classes
     * to define their specific route logic
     */
    protected abstract void configureRoute() throws Exception;

    public abstract boolean isAutoStartup();
}

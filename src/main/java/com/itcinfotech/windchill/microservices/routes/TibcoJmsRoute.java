package com.itcinfotech.windchill.microservices.routes;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Sample route that demonstrates TIBCO JMS integration with Apache Camel.
 * This route consumes messages from a TIBCO EMS queue and processes them.
 */
@Component
public class TibcoJmsRoute extends BaseRoute {
    
    @Value("${tibco.ems.queue.sample:SAMPLE.QUEUE}")
    private String inputQueue;
    
    @Value("${tibco.ems.queue.output:SAMPLE.OUTPUT.QUEUE}")
    private String outputQueue;
    
    @Override
    protected void configureRoute() throws Exception {
        // Route that consumes messages from SAMPLE.OUTPUT.QUEUE
        fromWithErrorHandling("tibco-jms-consumer", 
            "tibco-jms:queue:" + outputQueue + 
            "?destinationType=queue" + 
            "&autoStartup=true" + 
            "&pubSubNoLocal=true" + 
            "&subscriptionDurable=false" + 
            "&useMessageIDAsCorrelationID=false" + 
            "&acknowledgementMode=1") // Change AUTO_ACKNOWLEDGE to its numeric value 1
            .log("Received message from TIBCO EMS: ${body}")
            .process(exchange -> {
                // Process the message here
                String message = exchange.getIn().getBody(String.class);
                logger.info("Processing message from SAMPLE.OUTPUT.QUEUE: {}", message);
                
                // You can transform the message or perform business logic here
                exchange.getMessage().setBody("Processed output: " + message);
            })
            .to("log:com.itcinfotech.windchill.microservices.routes?level=INFO");
        
        // Route that sends messages to SAMPLE.QUEUE
        fromWithErrorHandling("tibco-jms-producer", "direct:sendToSample")
            .log("Sending message to SAMPLE.QUEUE: ${body}")
            .to("tibco-jms:queue:" + inputQueue + 
                "?destinationType=queue" + 
                "&autoStartup=true" + 
                "&useMessageIDAsCorrelationID=false" + 
                "&acknowledgementMode=1"); // Change AUTO_ACKNOWLEDGE to its numeric value 1
    }

    /**
     * Gets the route auto-startup setting
     */
    @Override
    public boolean isAutoStartup() {
        return true; // Auto-start TIBCO JMS routes by default
    }
}

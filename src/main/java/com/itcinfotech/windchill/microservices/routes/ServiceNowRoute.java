package com.itcinfotech.windchill.microservices.routes;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.itcinfotech.windchill.microservices.request.ServiceNowIncidentRequest;
import com.itcinfotech.windchill.microservices.response.ServiceNowIncidentResponse;
import com.itcinfotech.windchill.microservices.service.servicenow.ServiceNowService;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Camel route for ServiceNow integration using the ServiceNow component
 */
@Component
public class ServiceNowRoute extends BaseRoute {

    @Autowired
    private ServiceNowService serviceNowService;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${camel.servicenow.route.enabled:true}")
    private boolean routeEnabled;

    @Value("${camel.servicenow.route.auto-startup:true}")
    private boolean autoStartup;

    @Value("${servicenow.instance.url}")
    private String instanceUrl;

    @Value("${servicenow.username}")
    private String username;

    @Value("${servicenow.password}")
    private String password;

    @Override
    protected void configureRoute() throws Exception {
        if (!routeEnabled) {
            logger.info("ServiceNow route is disabled");
            return;
        }

        logger.info("Configuring ServiceNow Camel routes");

        // Route for creating incidents via Camel
        fromWithErrorHandling("servicenow-create-incident", "direct:servicenow-create-incident")
            .log("Creating ServiceNow incident via Camel: ${body}")
            .process(new CreateIncidentProcessor())
            .to("log:com.itcinfotech.windchill.microservices.routes.ServiceNowRoute?level=INFO");

        // Route for getting incidents via ServiceNow service (REST API)
        fromWithErrorHandling("servicenow-get-incidents", "direct:servicenow-get-incidents")
            .log("Getting ServiceNow incidents via REST API")
            .process(exchange -> {
                try {
                    // Use the ServiceNow service to get incidents
                    com.itcinfotech.windchill.microservices.response.ServiceNowListResponse response =
                        serviceNowService.getIncidents(null, 10, 0);

                    String responseJson = objectMapper.writeValueAsString(response);
                    exchange.getMessage().setBody(responseJson);

                    logger.info("ServiceNow incidents retrieved successfully via REST API");
                } catch (Exception e) {
                    logger.error("Error getting ServiceNow incidents via REST API", e);
                    exchange.getMessage().setBody("{\"status\":\"error\",\"message\":\"" + e.getMessage() + "\"}");
                }
            })
            .to("log:com.itcinfotech.windchill.microservices.routes.ServiceNowRoute?level=INFO");

        // Route for getting a specific incident via ServiceNow service (REST API)
        fromWithErrorHandling("servicenow-get-incident", "direct:servicenow-get-incident")
            .log("Getting ServiceNow incident via REST API: ${header.incidentId}")
            .process(exchange -> {
                try {
                    String incidentId = exchange.getIn().getHeader("incidentId", String.class);
                    if (incidentId == null || incidentId.isEmpty()) {
                        throw new IllegalArgumentException("Incident ID is required");
                    }

                    // Use the ServiceNow service to get the specific incident
                    com.itcinfotech.windchill.microservices.response.ServiceNowIncidentResponse response =
                        serviceNowService.getIncident(incidentId);

                    String responseJson = objectMapper.writeValueAsString(response);
                    exchange.getMessage().setBody(responseJson);

                    logger.info("ServiceNow incident {} retrieved successfully via REST API", incidentId);
                } catch (Exception e) {
                    logger.error("Error getting ServiceNow incident via REST API", e);
                    exchange.getMessage().setBody("{\"status\":\"error\",\"message\":\"" + e.getMessage() + "\"}");
                }
            })
            .to("log:com.itcinfotech.windchill.microservices.routes.ServiceNowRoute?level=INFO");

        // Route for creating incidents via ServiceNow component (using REST API for now)
        fromWithErrorHandling("servicenow-camel-create-incident", "direct:servicenow-camel-create-incident")
            .log("Creating ServiceNow incident via REST API: ${body}")
            .process(exchange -> {
                try {
                    // Parse the incident data
                    String requestBody = exchange.getIn().getBody(String.class);
                    logger.debug("Processing incident creation request: {}", requestBody);

                    // For now, create a simple incident using the service
                    com.itcinfotech.windchill.microservices.request.ServiceNowIncidentRequest request =
                        com.itcinfotech.windchill.microservices.request.ServiceNowIncidentRequest.builder()
                            .shortDescription("Test incident via Camel Component")
                            .description("Created via Camel ServiceNow component route")
                            .category("Software")
                            .urgency("3")
                            .impact("3")
                            .priority("5")
                            .uSource("Camel Component")
                            .build();

                    com.itcinfotech.windchill.microservices.response.ServiceNowIncidentResponse response =
                        serviceNowService.createIncident(request);

                    String responseJson = objectMapper.writeValueAsString(response);
                    exchange.getMessage().setBody(responseJson);

                    logger.info("ServiceNow incident created successfully via REST API");
                } catch (Exception e) {
                    logger.error("Error creating ServiceNow incident via REST API", e);
                    exchange.getMessage().setBody("{\"status\":\"error\",\"message\":\"" + e.getMessage() + "\"}");
                }
            })
            .to("log:com.itcinfotech.windchill.microservices.routes.ServiceNowRoute?level=INFO");

        // Route for updating incidents via ServiceNow component (using REST API for now)
        fromWithErrorHandling("servicenow-camel-update-incident", "direct:servicenow-camel-update-incident")
            .log("Updating ServiceNow incident via REST API: ${header.incidentId}")
            .process(exchange -> {
                try {
                    String requestBody = exchange.getIn().getBody(String.class);
                    String incidentId = exchange.getIn().getHeader("incidentId", String.class);

                    if (incidentId == null || incidentId.isEmpty()) {
                        throw new IllegalArgumentException("Incident ID is required for update");
                    }

                    logger.debug("Processing incident update request for {}: {}", incidentId, requestBody);

                    // For now, create a simple update request
                    com.itcinfotech.windchill.microservices.request.ServiceNowIncidentRequest request =
                        com.itcinfotech.windchill.microservices.request.ServiceNowIncidentRequest.builder()
                            .workNotes("Updated via Camel Component")
                            .uSource("Camel Component Update")
                            .build();

                    com.itcinfotech.windchill.microservices.response.ServiceNowIncidentResponse response =
                        serviceNowService.updateIncident(incidentId, request);

                    String responseJson = objectMapper.writeValueAsString(response);
                    exchange.getMessage().setBody(responseJson);

                    logger.info("ServiceNow incident {} updated successfully via REST API", incidentId);
                } catch (Exception e) {
                    logger.error("Error updating ServiceNow incident via REST API", e);
                    exchange.getMessage().setBody("{\"status\":\"error\",\"message\":\"" + e.getMessage() + "\"}");
                }
            })
            .to("log:com.itcinfotech.windchill.microservices.routes.ServiceNowRoute?level=INFO");

        // Route for handling ServiceNow webhooks/notifications
        fromWithErrorHandling("servicenow-webhook", "direct:servicenow-webhook")
            .log("Received ServiceNow webhook: ${body}")
            .process(exchange -> {
                String webhookData = exchange.getIn().getBody(String.class);
                logger.info("Processing ServiceNow webhook data: {}", webhookData);
                
                // Process webhook data here
                // You can add business logic to handle different types of ServiceNow notifications
                
                exchange.getMessage().setBody("Webhook processed successfully");
            })
            .to("log:com.itcinfotech.windchill.microservices.routes.ServiceNowRoute?level=INFO");

        // Route for batch processing ServiceNow incidents
        fromWithErrorHandling("servicenow-batch-process", "direct:servicenow-batch-process")
            .log("Starting ServiceNow batch processing")
            .process(exchange -> {
                logger.info("Processing batch ServiceNow operations");
                
                // Add batch processing logic here
                // This could include:
                // - Bulk incident creation
                // - Periodic incident status updates
                // - Data synchronization
                
                exchange.getMessage().setBody("Batch processing completed");
            })
            .to("log:com.itcinfotech.windchill.microservices.routes.ServiceNowRoute?level=INFO");

        logger.info("ServiceNow Camel routes configured successfully");
    }

    /**
     * Processor for creating incidents using the ServiceNow service
     */
    private class CreateIncidentProcessor implements Processor {
        @Override
        public void process(Exchange exchange) throws Exception {
            try {
                String requestBody = exchange.getIn().getBody(String.class);
                logger.debug("Processing incident creation request: {}", requestBody);
                
                // Parse the request
                ServiceNowIncidentRequest request = objectMapper.readValue(requestBody, ServiceNowIncidentRequest.class);
                
                // Create the incident using the service
                ServiceNowIncidentResponse response;
                if (request.getAttachments() != null && !request.getAttachments().isEmpty()) {
                    response = serviceNowService.createIncidentWithAttachments(request);
                } else {
                    response = serviceNowService.createIncident(request);
                }
                
                // Set the response
                String responseJson = objectMapper.writeValueAsString(response);
                exchange.getMessage().setBody(responseJson);
                
                // Set headers for downstream processing
                exchange.getMessage().setHeader("ServiceNowIncidentId", response.getSysId());
                exchange.getMessage().setHeader("ServiceNowIncidentNumber", response.getNumber());
                exchange.getMessage().setHeader("ServiceNowStatus", response.getStatus());
                
                logger.info("Incident created successfully via Camel: {} ({})", 
                           response.getNumber(), response.getSysId());
                
            } catch (Exception e) {
                logger.error("Error processing incident creation in Camel route", e);
                
                // Create error response
                ServiceNowIncidentResponse errorResponse = ServiceNowIncidentResponse.builder()
                    .status("error")
                    .error("Camel processing error: " + e.getMessage())
                    .message("Failed to create incident via Camel route")
                    .build();
                
                String errorJson = objectMapper.writeValueAsString(errorResponse);
                exchange.getMessage().setBody(errorJson);
                exchange.getMessage().setHeader("ServiceNowStatus", "error");
                
                // Don't rethrow the exception to allow the route to continue
            }
        }
    }

    /**
     * Gets the route auto-startup setting
     */
    @Override
    public boolean isAutoStartup() {
        return autoStartup;
    }
}

package com.itcinfotech.windchill.microservices.routes;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.itcinfotech.windchill.microservices.request.ServiceNowIncidentRequest;
import com.itcinfotech.windchill.microservices.response.ServiceNowIncidentResponse;
import com.itcinfotech.windchill.microservices.service.servicenow.ServiceNowService;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Camel route for ServiceNow integration using the ServiceNow component
 */
@Component
public class ServiceNowRoute extends BaseRoute {

    @Autowired
    private ServiceNowService serviceNowService;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${camel.servicenow.route.enabled:true}")
    private boolean routeEnabled;

    @Value("${camel.servicenow.route.auto-startup:true}")
    private boolean autoStartup;

    @Value("${servicenow.instance.url}")
    private String instanceUrl;

    @Value("${servicenow.username}")
    private String username;

    @Value("${servicenow.password}")
    private String password;

    @Override
    protected void configureRoute() throws Exception {
        if (!routeEnabled) {
            logger.info("ServiceNow route is disabled");
            return;
        }

        logger.info("Configuring ServiceNow Camel routes");

        // Route for creating incidents via Camel
        fromWithErrorHandling("servicenow-create-incident", "direct:servicenow-create-incident")
            .log("Creating ServiceNow incident via Camel: ${body}")
            .process(new CreateIncidentProcessor())
            .to("log:com.itcinfotech.windchill.microservices.routes.ServiceNowRoute?level=INFO");

        // Route for getting incidents via Camel ServiceNow component
        fromWithErrorHandling("servicenow-get-incidents", "direct:servicenow-get-incidents")
            .log("Getting ServiceNow incidents via Camel")
            .toD("servicenow:table?table=incident&instanceName=" + instanceUrl + 
                 "&userName=" + username + "&password=" + password + 
                 "&resource=table&action=retrieve&limit=10")
            .process(exchange -> {
                String response = exchange.getIn().getBody(String.class);
                logger.info("ServiceNow incidents response: {}", response);
                exchange.getMessage().setBody(response);
            })
            .to("log:com.itcinfotech.windchill.microservices.routes.ServiceNowRoute?level=INFO");

        // Route for getting a specific incident via Camel ServiceNow component
        fromWithErrorHandling("servicenow-get-incident", "direct:servicenow-get-incident")
            .log("Getting ServiceNow incident via Camel: ${header.incidentId}")
            .toD("servicenow:table?table=incident&instanceName=" + instanceUrl + 
                 "&userName=" + username + "&password=" + password + 
                 "&resource=table&action=retrieve&sysId=${header.incidentId}")
            .process(exchange -> {
                String response = exchange.getIn().getBody(String.class);
                logger.info("ServiceNow incident response: {}", response);
                exchange.getMessage().setBody(response);
            })
            .to("log:com.itcinfotech.windchill.microservices.routes.ServiceNowRoute?level=INFO");

        // Route for creating incidents via Camel ServiceNow component
        fromWithErrorHandling("servicenow-camel-create-incident", "direct:servicenow-camel-create-incident")
            .log("Creating ServiceNow incident via Camel component: ${body}")
            .process(exchange -> {
                // Prepare the incident data for ServiceNow component
                String requestBody = exchange.getIn().getBody(String.class);
                logger.debug("Processing incident creation request: {}", requestBody);
                
                // The body should contain the incident data in JSON format
                exchange.getMessage().setBody(requestBody);
                exchange.getMessage().setHeader("CamelServiceNowTable", "incident");
                exchange.getMessage().setHeader("CamelServiceNowAction", "create");
            })
            .toD("servicenow:table?table=incident&instanceName=" + instanceUrl + 
                 "&userName=" + username + "&password=" + password + 
                 "&resource=table&action=create")
            .process(exchange -> {
                String response = exchange.getIn().getBody(String.class);
                logger.info("ServiceNow incident created via Camel: {}", response);
                exchange.getMessage().setBody(response);
            })
            .to("log:com.itcinfotech.windchill.microservices.routes.ServiceNowRoute?level=INFO");

        // Route for updating incidents via Camel ServiceNow component
        fromWithErrorHandling("servicenow-camel-update-incident", "direct:servicenow-camel-update-incident")
            .log("Updating ServiceNow incident via Camel component: ${header.incidentId}")
            .process(exchange -> {
                String requestBody = exchange.getIn().getBody(String.class);
                String incidentId = exchange.getIn().getHeader("incidentId", String.class);
                
                logger.debug("Processing incident update request for {}: {}", incidentId, requestBody);
                
                exchange.getMessage().setBody(requestBody);
                exchange.getMessage().setHeader("CamelServiceNowTable", "incident");
                exchange.getMessage().setHeader("CamelServiceNowAction", "update");
                exchange.getMessage().setHeader("CamelServiceNowSysId", incidentId);
            })
            .toD("servicenow:table?table=incident&instanceName=" + instanceUrl + 
                 "&userName=" + username + "&password=" + password + 
                 "&resource=table&action=update&sysId=${header.incidentId}")
            .process(exchange -> {
                String response = exchange.getIn().getBody(String.class);
                logger.info("ServiceNow incident updated via Camel: {}", response);
                exchange.getMessage().setBody(response);
            })
            .to("log:com.itcinfotech.windchill.microservices.routes.ServiceNowRoute?level=INFO");

        // Route for handling ServiceNow webhooks/notifications
        fromWithErrorHandling("servicenow-webhook", "direct:servicenow-webhook")
            .log("Received ServiceNow webhook: ${body}")
            .process(exchange -> {
                String webhookData = exchange.getIn().getBody(String.class);
                logger.info("Processing ServiceNow webhook data: {}", webhookData);
                
                // Process webhook data here
                // You can add business logic to handle different types of ServiceNow notifications
                
                exchange.getMessage().setBody("Webhook processed successfully");
            })
            .to("log:com.itcinfotech.windchill.microservices.routes.ServiceNowRoute?level=INFO");

        // Route for batch processing ServiceNow incidents
        fromWithErrorHandling("servicenow-batch-process", "direct:servicenow-batch-process")
            .log("Starting ServiceNow batch processing")
            .process(exchange -> {
                logger.info("Processing batch ServiceNow operations");
                
                // Add batch processing logic here
                // This could include:
                // - Bulk incident creation
                // - Periodic incident status updates
                // - Data synchronization
                
                exchange.getMessage().setBody("Batch processing completed");
            })
            .to("log:com.itcinfotech.windchill.microservices.routes.ServiceNowRoute?level=INFO");

        logger.info("ServiceNow Camel routes configured successfully");
    }

    /**
     * Processor for creating incidents using the ServiceNow service
     */
    private class CreateIncidentProcessor implements Processor {
        @Override
        public void process(Exchange exchange) throws Exception {
            try {
                String requestBody = exchange.getIn().getBody(String.class);
                logger.debug("Processing incident creation request: {}", requestBody);
                
                // Parse the request
                ServiceNowIncidentRequest request = objectMapper.readValue(requestBody, ServiceNowIncidentRequest.class);
                
                // Create the incident using the service
                ServiceNowIncidentResponse response;
                if (request.getAttachments() != null && !request.getAttachments().isEmpty()) {
                    response = serviceNowService.createIncidentWithAttachments(request);
                } else {
                    response = serviceNowService.createIncident(request);
                }
                
                // Set the response
                String responseJson = objectMapper.writeValueAsString(response);
                exchange.getMessage().setBody(responseJson);
                
                // Set headers for downstream processing
                exchange.getMessage().setHeader("ServiceNowIncidentId", response.getSysId());
                exchange.getMessage().setHeader("ServiceNowIncidentNumber", response.getNumber());
                exchange.getMessage().setHeader("ServiceNowStatus", response.getStatus());
                
                logger.info("Incident created successfully via Camel: {} ({})", 
                           response.getNumber(), response.getSysId());
                
            } catch (Exception e) {
                logger.error("Error processing incident creation in Camel route", e);
                
                // Create error response
                ServiceNowIncidentResponse errorResponse = ServiceNowIncidentResponse.builder()
                    .status("error")
                    .error("Camel processing error: " + e.getMessage())
                    .message("Failed to create incident via Camel route")
                    .build();
                
                String errorJson = objectMapper.writeValueAsString(errorResponse);
                exchange.getMessage().setBody(errorJson);
                exchange.getMessage().setHeader("ServiceNowStatus", "error");
                
                // Don't rethrow the exception to allow the route to continue
            }
        }
    }

    /**
     * Gets the route auto-startup setting
     */
    @Override
    public boolean isAutoStartup() {
        return autoStartup;
    }
}

package com.itcinfotech.windchill.microservices.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Utility class for ServiceNow file operations
 */
public class ServiceNowFileUtils {

    private static final Logger logger = LoggerFactory.getLogger(ServiceNowFileUtils.class);

    // Maximum file size (10MB)
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024;

    // Allowed file extensions
    private static final List<String> ALLOWED_EXTENSIONS = Arrays.asList(
        "pdf", "doc", "docx", "txt", "jpg", "jpeg", "png", "gif", "zip", "csv", "xlsx", "xls",
        "ppt", "pptx", "rtf", "xml", "json", "log", "bmp", "tiff", "mp4", "avi", "mov"
    );

    /**
     * Validates a list of MultipartFile objects for ServiceNow upload
     */
    public static void validateAttachments(List<MultipartFile> files) throws IllegalArgumentException {
        if (files == null || files.isEmpty()) {
            return;
        }

        for (MultipartFile file : files) {
            validateAttachment(file);
        }
    }

    /**
     * Validates a single MultipartFile for ServiceNow upload
     */
    public static void validateAttachment(MultipartFile file) throws IllegalArgumentException {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("File cannot be null or empty");
        }

        // Check file size
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new IllegalArgumentException(
                String.format("File '%s' exceeds maximum size of %d bytes", 
                             file.getOriginalFilename(), MAX_FILE_SIZE));
        }

        // Check file extension
        String filename = file.getOriginalFilename();
        if (filename == null || filename.trim().isEmpty()) {
            throw new IllegalArgumentException("File must have a valid filename");
        }

        String extension = getFileExtension(filename);
        if (extension == null || !ALLOWED_EXTENSIONS.contains(extension.toLowerCase())) {
            throw new IllegalArgumentException(
                String.format("File '%s' has unsupported extension. Allowed extensions: %s", 
                             filename, String.join(", ", ALLOWED_EXTENSIONS)));
        }

        logger.debug("File validation passed for: {} (size: {} bytes, extension: {})", 
                    filename, file.getSize(), extension);
    }

    /**
     * Converts MultipartFile objects to File objects in a temporary directory
     */
    public static List<File> convertMultipartFilesToFiles(List<MultipartFile> multipartFiles, String tempDirectory) 
            throws IOException {
        
        if (multipartFiles == null || multipartFiles.isEmpty()) {
            return new ArrayList<>();
        }

        List<File> files = new ArrayList<>();
        Path tempDir = Paths.get(tempDirectory);

        // Create temp directory if it doesn't exist
        if (!Files.exists(tempDir)) {
            Files.createDirectories(tempDir);
            logger.debug("Created temporary directory: {}", tempDir.toAbsolutePath());
        }

        for (MultipartFile multipartFile : multipartFiles) {
            if (multipartFile.isEmpty()) {
                logger.warn("Skipping empty file: {}", multipartFile.getOriginalFilename());
                continue;
            }

            try {
                File file = convertMultipartFileToFile(multipartFile, tempDirectory);
                files.add(file);
                logger.debug("Converted MultipartFile to File: {}", file.getAbsolutePath());
            } catch (IOException e) {
                logger.error("Failed to convert MultipartFile to File: {}", multipartFile.getOriginalFilename(), e);
                // Clean up any files that were successfully created
                cleanupFiles(files);
                throw e;
            }
        }

        return files;
    }

    /**
     * Converts a single MultipartFile to a File in the specified directory
     */
    public static File convertMultipartFileToFile(MultipartFile multipartFile, String tempDirectory) 
            throws IOException {
        
        String originalFilename = multipartFile.getOriginalFilename();
        if (originalFilename == null) {
            originalFilename = "attachment_" + System.currentTimeMillis();
        }

        // Sanitize filename
        String sanitizedFilename = sanitizeFilename(originalFilename);
        
        Path tempDir = Paths.get(tempDirectory);
        Path tempFile = tempDir.resolve(sanitizedFilename);

        // If file already exists, append timestamp
        if (Files.exists(tempFile)) {
            String nameWithoutExt = getFilenameWithoutExtension(sanitizedFilename);
            String extension = getFileExtension(sanitizedFilename);
            String timestampedName = nameWithoutExt + "_" + System.currentTimeMillis() + 
                                   (extension != null ? "." + extension : "");
            tempFile = tempDir.resolve(timestampedName);
        }

        // Copy the file
        Files.copy(multipartFile.getInputStream(), tempFile, StandardCopyOption.REPLACE_EXISTING);
        
        File file = tempFile.toFile();
        logger.debug("Created temporary file: {} (size: {} bytes)", 
                    file.getAbsolutePath(), file.length());
        
        return file;
    }

    /**
     * Cleans up a list of temporary files
     */
    public static void cleanupFiles(List<File> files) {
        if (files == null || files.isEmpty()) {
            return;
        }

        for (File file : files) {
            try {
                if (file.exists() && file.delete()) {
                    logger.debug("Deleted temporary file: {}", file.getAbsolutePath());
                } else {
                    logger.warn("Failed to delete temporary file: {}", file.getAbsolutePath());
                }
            } catch (Exception e) {
                logger.warn("Error deleting temporary file: {}", file.getAbsolutePath(), e);
            }
        }
    }

    /**
     * Gets the file extension from a filename
     */
    public static String getFileExtension(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return null;
        }

        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return null;
        }

        return filename.substring(lastDotIndex + 1);
    }

    /**
     * Gets the filename without extension
     */
    public static String getFilenameWithoutExtension(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return filename;
        }

        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return filename;
        }

        return filename.substring(0, lastDotIndex);
    }

    /**
     * Sanitizes a filename by removing or replacing invalid characters
     */
    public static String sanitizeFilename(String filename) {
        if (filename == null) {
            return "attachment_" + System.currentTimeMillis();
        }

        // Remove or replace invalid characters
        String sanitized = filename.replaceAll("[^a-zA-Z0-9._-]", "_");
        
        // Ensure filename is not too long
        if (sanitized.length() > 255) {
            String extension = getFileExtension(sanitized);
            String nameWithoutExt = getFilenameWithoutExtension(sanitized);
            
            int maxNameLength = 255 - (extension != null ? extension.length() + 1 : 0);
            nameWithoutExt = nameWithoutExt.substring(0, Math.min(nameWithoutExt.length(), maxNameLength));
            
            sanitized = nameWithoutExt + (extension != null ? "." + extension : "");
        }

        return sanitized;
    }

    /**
     * Gets the MIME type for a file based on its extension
     */
    public static String getMimeType(String filename) {
        if (filename == null) {
            return "application/octet-stream";
        }

        String extension = getFileExtension(filename);
        if (extension == null) {
            return "application/octet-stream";
        }

        switch (extension.toLowerCase()) {
            case "pdf":
                return "application/pdf";
            case "doc":
                return "application/msword";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "xls":
                return "application/vnd.ms-excel";
            case "xlsx":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "ppt":
                return "application/vnd.ms-powerpoint";
            case "pptx":
                return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
            case "txt":
                return "text/plain";
            case "csv":
                return "text/csv";
            case "xml":
                return "application/xml";
            case "json":
                return "application/json";
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";
            case "tiff":
                return "image/tiff";
            case "zip":
                return "application/zip";
            case "mp4":
                return "video/mp4";
            case "avi":
                return "video/x-msvideo";
            case "mov":
                return "video/quicktime";
            default:
                return "application/octet-stream";
        }
    }

    /**
     * Formats file size in human-readable format
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        }
        
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.1f %sB", bytes / Math.pow(1024, exp), pre);
    }

    /**
     * Gets the maximum allowed file size
     */
    public static long getMaxFileSize() {
        return MAX_FILE_SIZE;
    }

    /**
     * Gets the list of allowed file extensions
     */
    public static List<String> getAllowedExtensions() {
        return new ArrayList<>(ALLOWED_EXTENSIONS);
    }
}

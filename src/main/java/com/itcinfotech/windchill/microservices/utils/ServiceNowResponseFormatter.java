package com.itcinfotech.windchill.microservices.utils;

import com.itcinfotech.windchill.microservices.dto.servicenow.ServiceNowAttachment;
import com.itcinfotech.windchill.microservices.dto.servicenow.ServiceNowIncident;
import com.itcinfotech.windchill.microservices.response.ServiceNowIncidentResponse;
import com.itcinfotech.windchill.microservices.response.ServiceNowListResponse;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Utility class for formatting ServiceNow API responses in a user-friendly way
 */
@Component
public class ServiceNowResponseFormatter {

    private static final DateTimeFormatter DISPLAY_DATE_FORMAT = DateTimeFormatter.ofPattern("MMM dd, yyyy 'at' hh:mm a");
    private static final DateTimeFormatter ISO_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * Formats a ServiceNow incident response for better readability
     */
    public Map<String, Object> formatIncidentResponse(ServiceNowIncidentResponse response) {
        Map<String, Object> formatted = new LinkedHashMap<>();
        
        // Response metadata
        formatted.put("status", response.getStatus());
        formatted.put("message", response.getMessage());
        
        if ("error".equals(response.getStatus())) {
            formatted.put("error", response.getError());
            return formatted;
        }
        
        // Incident summary
        if (response.getResult() != null) {
            formatted.put("incident_summary", formatIncidentSummary(response.getResult()));
            formatted.put("incident_details", formatIncidentDetails(response.getResult()));
        }
        
        // Quick access fields
        Map<String, Object> quickAccess = new LinkedHashMap<>();
        quickAccess.put("incident_number", response.getNumber());
        quickAccess.put("sys_id", response.getSysId());
        quickAccess.put("state", formatState(response.getState()));
        quickAccess.put("incident_url", response.getIncidentUrl());
        formatted.put("quick_access", quickAccess);
        
        // Attachments if present
        if (response.getAttachments() != null && !response.getAttachments().isEmpty()) {
            formatted.put("attachments", formatAttachments(response.getAttachments()));
        }
        
        // Timestamps
        Map<String, Object> timestamps = new LinkedHashMap<>();
        if (response.getCreatedOn() != null) {
            timestamps.put("created", formatTimestamp(response.getCreatedOn()));
        }
        if (response.getUpdatedOn() != null) {
            timestamps.put("updated", formatTimestamp(response.getUpdatedOn()));
        }
        if (!timestamps.isEmpty()) {
            formatted.put("timestamps", timestamps);
        }
        
        return formatted;
    }

    /**
     * Formats a ServiceNow list response for better readability
     */
    public Map<String, Object> formatListResponse(ServiceNowListResponse response) {
        Map<String, Object> formatted = new LinkedHashMap<>();
        
        // Response metadata
        formatted.put("status", response.getStatus());
        formatted.put("message", response.getMessage());
        
        if ("error".equals(response.getStatus())) {
            formatted.put("error", response.getError());
            return formatted;
        }
        
        // Summary
        Map<String, Object> summary = new LinkedHashMap<>();
        summary.put("total_incidents", response.getTotalCount());
        summary.put("page", response.getPage());
        summary.put("page_size", response.getPageSize());
        formatted.put("summary", summary);
        
        // Incidents list
        if (response.getResult() != null && !response.getResult().isEmpty()) {
            formatted.put("incidents", response.getResult().stream()
                .map(this::formatIncidentSummary)
                .collect(Collectors.toList()));
        }
        
        return formatted;
    }

    /**
     * Formats incident summary information
     */
    private Map<String, Object> formatIncidentSummary(ServiceNowIncident incident) {
        Map<String, Object> summary = new LinkedHashMap<>();
        
        summary.put("number", incident.getNumber());
        summary.put("short_description", incident.getShortDescription());
        summary.put("state", formatState(incident.getState()));
        summary.put("priority", formatPriority(incident.getPriority()));
        summary.put("urgency", formatUrgency(incident.getUrgency()));
        summary.put("impact", formatImpact(incident.getImpact()));
        summary.put("category", incident.getCategory());
        summary.put("assigned_to", formatUserField(incident.getAssignedTo()));
        summary.put("assignment_group", formatUserField(incident.getAssignmentGroup()));
        summary.put("sys_id", incident.getSysId());
        
        return summary;
    }

    /**
     * Formats detailed incident information
     */
    private Map<String, Object> formatIncidentDetails(ServiceNowIncident incident) {
        Map<String, Object> details = new LinkedHashMap<>();
        
        // Basic information
        Map<String, Object> basicInfo = new LinkedHashMap<>();
        basicInfo.put("description", incident.getDescription());
        basicInfo.put("caller", formatUserField(incident.getCallerId()));
        basicInfo.put("opened_by", formatUserField(incident.getOpenedBy()));
        basicInfo.put("contact_type", incident.getContactType());
        basicInfo.put("location", formatUserField(incident.getLocation()));
        basicInfo.put("company", formatUserField(incident.getCompany()));
        details.put("basic_information", basicInfo);
        
        // Classification
        Map<String, Object> classification = new LinkedHashMap<>();
        classification.put("category", incident.getCategory());
        classification.put("subcategory", incident.getSubcategory());
        classification.put("business_service", formatUserField(incident.getBusinessService()));
        classification.put("cmdb_ci", formatUserField(incident.getCmdbCi()));
        details.put("classification", classification);
        
        // Assignment
        Map<String, Object> assignment = new LinkedHashMap<>();
        assignment.put("assigned_to", formatUserField(incident.getAssignedTo()));
        assignment.put("assignment_group", formatUserField(incident.getAssignmentGroup()));
        details.put("assignment", assignment);
        
        // Resolution
        Map<String, Object> resolution = new LinkedHashMap<>();
        resolution.put("resolved_by", formatUserField(incident.getResolvedBy()));
        resolution.put("closed_by", formatUserField(incident.getClosedBy()));
        resolution.put("resolution_code", incident.getResolutionCode());
        resolution.put("resolution_notes", incident.getResolutionNotes());
        details.put("resolution", resolution);
        
        // Work notes and comments
        Map<String, Object> notes = new LinkedHashMap<>();
        notes.put("work_notes", incident.getWorkNotes());
        notes.put("comments", incident.getComments());
        notes.put("close_notes", incident.getCloseNotes());
        details.put("notes", notes);
        
        // Custom fields
        Map<String, Object> customFields = new LinkedHashMap<>();
        customFields.put("source", incident.getUSource());
        customFields.put("environment", incident.getUEnvironment());
        customFields.put("application", incident.getUApplication());
        customFields.put("correlation_id", incident.getCorrelationId());
        details.put("custom_fields", customFields);
        
        return details;
    }

    /**
     * Formats attachment information
     */
    private List<Map<String, Object>> formatAttachments(List<ServiceNowAttachment> attachments) {
        return attachments.stream().map(attachment -> {
            Map<String, Object> formatted = new LinkedHashMap<>();
            formatted.put("file_name", attachment.getFileName());
            formatted.put("content_type", attachment.getContentType());
            formatted.put("size", formatFileSize(attachment.getSizeBytes()));
            formatted.put("download_link", attachment.getDownloadLink());
            formatted.put("sys_id", attachment.getSysId());
            return formatted;
        }).collect(Collectors.toList());
    }

    /**
     * Formats state values to human-readable text
     */
    private String formatState(String state) {
        if (state == null) return "Unknown";
        
        switch (state) {
            case "1": return "New";
            case "2": return "In Progress";
            case "3": return "On Hold";
            case "6": return "Resolved";
            case "7": return "Closed";
            case "8": return "Canceled";
            default: return "State " + state;
        }
    }

    /**
     * Formats priority values to human-readable text
     */
    private String formatPriority(String priority) {
        if (priority == null) return "Not Set";
        
        switch (priority) {
            case "1": return "1 - Critical";
            case "2": return "2 - High";
            case "3": return "3 - Moderate";
            case "4": return "4 - Low";
            case "5": return "5 - Planning";
            default: return "Priority " + priority;
        }
    }

    /**
     * Formats urgency values to human-readable text
     */
    private String formatUrgency(String urgency) {
        if (urgency == null) return "Not Set";
        
        switch (urgency) {
            case "1": return "1 - High";
            case "2": return "2 - Medium";
            case "3": return "3 - Low";
            default: return "Urgency " + urgency;
        }
    }

    /**
     * Formats impact values to human-readable text
     */
    private String formatImpact(String impact) {
        if (impact == null) return "Not Set";
        
        switch (impact) {
            case "1": return "1 - High";
            case "2": return "2 - Medium";
            case "3": return "3 - Low";
            default: return "Impact " + impact;
        }
    }

    /**
     * Formats user/reference fields
     */
    private String formatUserField(String userField) {
        if (userField == null || userField.trim().isEmpty()) {
            return "Not Assigned";
        }
        return userField;
    }

    /**
     * Formats file size in human-readable format
     */
    private String formatFileSize(Long bytes) {
        if (bytes == null || bytes == 0) return "0 B";
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = bytes.doubleValue();
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.1f %s", size, units[unitIndex]);
    }

    /**
     * Formats timestamp for display
     */
    private Map<String, String> formatTimestamp(String timestamp) {
        Map<String, String> formatted = new LinkedHashMap<>();
        formatted.put("raw", timestamp);
        
        try {
            // Try to parse and format the timestamp
            // ServiceNow typically returns timestamps in various formats
            if (timestamp != null && !timestamp.trim().isEmpty()) {
                formatted.put("display", timestamp); // For now, just use the raw value
                // You can add more sophisticated date parsing here if needed
            }
        } catch (Exception e) {
            formatted.put("display", timestamp);
        }
        
        return formatted;
    }

    /**
     * Creates a simple success response
     */
    public Map<String, Object> createSuccessResponse(String message, Object data) {
        Map<String, Object> response = new LinkedHashMap<>();
        response.put("status", "success");
        response.put("message", message);
        response.put("timestamp", LocalDateTime.now().format(ISO_DATE_FORMAT));
        if (data != null) {
            response.put("data", data);
        }
        return response;
    }

    /**
     * Creates a simple error response
     */
    public Map<String, Object> createErrorResponse(String message, String error) {
        Map<String, Object> response = new LinkedHashMap<>();
        response.put("status", "error");
        response.put("message", message);
        response.put("error", error);
        response.put("timestamp", LocalDateTime.now().format(ISO_DATE_FORMAT));
        return response;
    }
}

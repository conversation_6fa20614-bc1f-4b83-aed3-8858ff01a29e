package com.itcinfotech.windchill.microservices.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * Custom deserializer for ServiceNow reference fields that can be either strings or objects
 */
public class ServiceNowReferenceDeserializer extends JsonDeserializer<String> {

    private static final Logger logger = LoggerFactory.getLogger(ServiceNowReferenceDeserializer.class);

    @Override
    public String deserialize(JsonParser parser, DeserializationContext context) throws IOException {
        JsonToken token = parser.getCurrentToken();
        
        if (token == JsonToken.VALUE_STRING) {
            // Simple string value
            return parser.getValueAsString();
        } else if (token == JsonToken.START_OBJECT) {
            // Object value - extract the value or display_value
            JsonNode node = parser.readValueAsTree();
            
            // Try to get display_value first (human-readable), then value, then link
            if (node.has("display_value") && !node.get("display_value").isNull()) {
                return node.get("display_value").asText();
            } else if (node.has("value") && !node.get("value").isNull()) {
                return node.get("value").asText();
            } else if (node.has("link") && !node.get("link").isNull()) {
                return node.get("link").asText();
            } else {
                // Return the entire object as string if no standard fields found
                return node.toString();
            }
        } else if (token == JsonToken.VALUE_NULL) {
            return null;
        } else {
            // For any other token type, try to convert to string
            return parser.getValueAsString();
        }
    }
}

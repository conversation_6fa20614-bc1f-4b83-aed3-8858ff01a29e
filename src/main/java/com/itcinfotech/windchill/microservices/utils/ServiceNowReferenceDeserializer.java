package com.itcinfotech.windchill.microservices.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * Custom deserializer for ServiceNow reference fields that can be either strings or objects
 */
public class ServiceNowReferenceDeserializer extends JsonDeserializer<String> {

    private static final Logger logger = LoggerFactory.getLogger(ServiceNowReferenceDeserializer.class);

    @Override
    public String deserialize(JsonParser parser, DeserializationContext context) throws IOException {
        JsonToken token = parser.getCurrentToken();

        try {
            if (token == JsonToken.VALUE_STRING) {
                // Simple string value
                return parser.getValueAsString();
            } else if (token == JsonToken.START_OBJECT) {
                // Object value - extract the value or display_value
                JsonNode node = parser.readValueAsTree();

                // Try to get display_value first (human-readable), then value, then link
                if (node.has("display_value") && !node.get("display_value").isNull()) {
                    String displayValue = node.get("display_value").asText();
                    if (displayValue != null && !displayValue.trim().isEmpty()) {
                        return displayValue;
                    }
                }

                if (node.has("value") && !node.get("value").isNull()) {
                    String value = node.get("value").asText();
                    if (value != null && !value.trim().isEmpty()) {
                        return value;
                    }
                }

                if (node.has("link") && !node.get("link").isNull()) {
                    String link = node.get("link").asText();
                    if (link != null && !link.trim().isEmpty()) {
                        return link;
                    }
                }

                // Try other common ServiceNow reference field names
                if (node.has("sys_id") && !node.get("sys_id").isNull()) {
                    return node.get("sys_id").asText();
                }

                if (node.has("name") && !node.get("name").isNull()) {
                    return node.get("name").asText();
                }

                // If it's an empty object, return empty string
                if (node.size() == 0) {
                    return "";
                }

                // Log the problematic object for debugging
                logger.debug("ServiceNow reference object with unexpected structure: {}", node.toString());

                // Return the entire object as string if no standard fields found
                return node.toString();
            } else if (token == JsonToken.VALUE_NULL) {
                return null;
            } else if (token == JsonToken.VALUE_NUMBER_INT || token == JsonToken.VALUE_NUMBER_FLOAT) {
                // Handle numeric values
                return parser.getValueAsString();
            } else if (token == JsonToken.VALUE_TRUE || token == JsonToken.VALUE_FALSE) {
                // Handle boolean values
                return parser.getValueAsString();
            } else {
                // For any other token type, try to convert to string
                String value = parser.getValueAsString();
                logger.debug("ServiceNow reference field with unexpected token type {}: {}", token, value);
                return value;
            }
        } catch (Exception e) {
            logger.warn("Error deserializing ServiceNow reference field, returning empty string", e);
            return "";
        }
    }
}

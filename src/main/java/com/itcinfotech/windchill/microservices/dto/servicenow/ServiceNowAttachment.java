package com.itcinfotech.windchill.microservices.dto.servicenow;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ServiceNow Attachment DTO representing an attachment record in ServiceNow
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ServiceNowAttachment {

    @JsonProperty("sys_id")
    private String sysId;

    @JsonProperty("file_name")
    private String fileName;

    @JsonProperty("content_type")
    private String contentType;

    @JsonProperty("size_bytes")
    private String sizeBytes;

    @JsonProperty("size_compressed")
    private String sizeCompressed;

    @JsonProperty("compressed")
    private String compressed;

    @JsonProperty("state")
    private String state;

    @JsonProperty("table_name")
    private String tableName;

    @JsonProperty("table_sys_id")
    private String tableSysId;

    @JsonProperty("download_link")
    private String downloadLink;

    @JsonProperty("sys_created_on")
    private String sysCreatedOn;

    @JsonProperty("sys_created_by")
    private String sysCreatedBy;

    @JsonProperty("sys_updated_on")
    private String sysUpdatedOn;

    @JsonProperty("sys_updated_by")
    private String sysUpdatedBy;

    @JsonProperty("sys_mod_count")
    private String sysModCount;

    @JsonProperty("sys_tags")
    private String sysTags;

    @JsonProperty("hash")
    private String hash;

    @JsonProperty("chunk_size_bytes")
    private String chunkSizeBytes;

    @JsonProperty("average_image_color")
    private String averageImageColor;

    @JsonProperty("image_width")
    private String imageWidth;

    @JsonProperty("image_height")
    private String imageHeight;

    @JsonProperty("sys_class_name")
    private String sysClassName;

    @JsonProperty("sys_domain")
    private String sysDomain;

    @JsonProperty("sys_domain_path")
    private String sysDomainPath;
}

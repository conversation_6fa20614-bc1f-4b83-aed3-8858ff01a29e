package com.itcinfotech.windchill.microservices.dto.servicenow;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * ServiceNow Incident DTO representing an incident record in ServiceNow
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ServiceNowIncident {

    @JsonProperty("sys_id")
    private String sysId;

    @JsonProperty("number")
    private String number;

    @JsonProperty("short_description")
    private String shortDescription;

    @JsonProperty("description")
    private String description;

    @JsonProperty("caller_id")
    private String callerId;

    @JsonProperty("category")
    private String category;

    @JsonProperty("subcategory")
    private String subcategory;

    @JsonProperty("urgency")
    private String urgency;

    @JsonProperty("impact")
    private String impact;

    @JsonProperty("priority")
    private String priority;

    @JsonProperty("state")
    private String state;

    @JsonProperty("assignment_group")
    private String assignmentGroup;

    @JsonProperty("assigned_to")
    private String assignedTo;

    @JsonProperty("work_notes")
    private String workNotes;

    @JsonProperty("close_notes")
    private String closeNotes;

    @JsonProperty("resolution_code")
    private String resolutionCode;

    @JsonProperty("sys_created_on")
    private String sysCreatedOn;

    @JsonProperty("sys_updated_on")
    private String sysUpdatedOn;

    @JsonProperty("opened_at")
    private String openedAt;

    @JsonProperty("closed_at")
    private String closedAt;

    @JsonProperty("resolved_at")
    private String resolvedAt;

    @JsonProperty("opened_by")
    private String openedBy;

    @JsonProperty("closed_by")
    private String closedBy;

    @JsonProperty("resolved_by")
    private String resolvedBy;

    @JsonProperty("contact_type")
    private String contactType;

    @JsonProperty("location")
    private String location;

    @JsonProperty("company")
    private String company;

    @JsonProperty("business_service")
    private String businessService;

    @JsonProperty("cmdb_ci")
    private String cmdbCi;

    @JsonProperty("correlation_id")
    private String correlationId;

    @JsonProperty("correlation_display")
    private String correlationDisplay;

    @JsonProperty("made_sla")
    private String madeSla;

    @JsonProperty("watch_list")
    private String watchList;

    @JsonProperty("upon_reject")
    private String uponReject;

    @JsonProperty("upon_approval")
    private String uponApproval;

    @JsonProperty("approval")
    private String approval;

    @JsonProperty("approval_history")
    private String approvalHistory;

    @JsonProperty("approval_set")
    private String approvalSet;

    @JsonProperty("universal_request")
    private String universalRequest;

    @JsonProperty("escalation")
    private String escalation;

    @JsonProperty("comments")
    private String comments;

    @JsonProperty("comments_and_work_notes")
    private String commentsAndWorkNotes;

    @JsonProperty("due_date")
    private String dueDate;

    @JsonProperty("sys_class_name")
    private String sysClassName;

    @JsonProperty("sys_domain")
    private String sysDomain;

    @JsonProperty("sys_domain_path")
    private String sysDomainPath;

    @JsonProperty("follow_up")
    private String followUp;

    @JsonProperty("group_list")
    private String groupList;

    @JsonProperty("reassignment_count")
    private String reassignmentCount;

    @JsonProperty("rejection_goto")
    private String rejectionGoto;

    @JsonProperty("route_reason")
    private String routeReason;

    @JsonProperty("sys_mod_count")
    private String sysModCount;

    @JsonProperty("sys_tags")
    private String sysTags;

    @JsonProperty("task_effective_number")
    private String taskEffectiveNumber;

    @JsonProperty("additional_assignee_list")
    private String additionalAssigneeList;

    @JsonProperty("business_duration")
    private String businessDuration;

    @JsonProperty("calendar_duration")
    private String calendarDuration;

    @JsonProperty("knowledge")
    private String knowledge;

    @JsonProperty("order")
    private String order;

    @JsonProperty("parent")
    private String parent;

    @JsonProperty("problem_id")
    private String problemId;

    @JsonProperty("rfc")
    private String rfc;

    @JsonProperty("vendor")
    private String vendor;

    @JsonProperty("caused_by")
    private String causedBy;

    @JsonProperty("child_incidents")
    private String childIncidents;

    @JsonProperty("hold_reason")
    private String holdReason;

    @JsonProperty("incident_state")
    private String incidentState;

    @JsonProperty("notify")
    private String notify;

    @JsonProperty("origin_id")
    private String originId;

    @JsonProperty("origin_table")
    private String originTable;

    @JsonProperty("parent_incident")
    private String parentIncident;

    @JsonProperty("problem_id_display")
    private String problemIdDisplay;

    @JsonProperty("reopen_count")
    private String reopenCount;

    @JsonProperty("reopened_by")
    private String reopenedBy;

    @JsonProperty("reopened_time")
    private String reopenedTime;

    @JsonProperty("resolved_by_display")
    private String resolvedByDisplay;

    @JsonProperty("rfc_display")
    private String rfcDisplay;

    @JsonProperty("severity")
    private String severity;

    @JsonProperty("sla_due")
    private String slaDue;

    @JsonProperty("subcategory_display")
    private String subcategoryDisplay;

    @JsonProperty("time_worked")
    private String timeWorked;

    @JsonProperty("user_input")
    private String userInput;

    @JsonProperty("calendar_stc")
    private String calendarStc;

    @JsonProperty("business_stc")
    private String businessStc;

    @JsonProperty("u_source")
    private String uSource;

    @JsonProperty("u_environment")
    private String uEnvironment;

    @JsonProperty("u_application")
    private String uApplication;
}

package com.itcinfotech.windchill.microservices.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.itcinfotech.windchill.microservices.dto.servicenow.ServiceNowIncident;
import com.itcinfotech.windchill.microservices.dto.servicenow.ServiceNowAttachment;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Response DTO for ServiceNow incident operations
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ServiceNowIncidentResponse {

    @JsonProperty("result")
    private ServiceNowIncident result;

    @JsonProperty("attachments")
    private List<ServiceNowAttachment> attachments;

    @JsonProperty("status")
    private String status;

    @JsonProperty("message")
    private String message;

    @JsonProperty("error")
    private String error;

    @JsonProperty("incident_url")
    private String incidentUrl;

    @JsonProperty("sys_id")
    private String sysId;

    @JsonProperty("number")
    private String number;

    @JsonProperty("state")
    private String state;

    @JsonProperty("created_on")
    private String createdOn;

    @JsonProperty("updated_on")
    private String updatedOn;
}

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.4</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.itcinfotech</groupId>
	<artifactId>windchill.microservices</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>windchill.microservices</name>
	<description>Microservice for Windchill</description>
	<url/>
	<licenses>
		<license/>
	</licenses>
	<developers>
		<developer/>
	</developers>
	<scm>
		<connection/>
		<developerConnection/>
		<tag/>
		<url/>
	</scm>
	<properties>
		<java.version>17</java.version>
		<azure.version>12.29.0</azure.version>
		<camel.version>4.8.1</camel.version>
	</properties>
	<repositories>
		<repository>
			<id>central</id>
			<url>https://repo.maven.apache.org/maven2</url>
		</repository>


	</repositories>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-webflux</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.14.0</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>2.14.0</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-jsr310</artifactId>
			<version>2.14.0</version>
		</dependency>
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
			<version>2.8.8</version>
		</dependency>
		<dependency>
			<groupId>com.azure</groupId>
			<artifactId>azure-storage-blob</artifactId>
			<version>12.28.1</version>
		</dependency>
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>s3</artifactId>
			<version>2.17.250</version>
		</dependency>
		<dependency>
			<groupId>org.apache.camel.springboot</groupId>
			<artifactId>camel-aws2-s3-starter</artifactId>
			<version>${camel.version}</version>
		</dependency>

		<!-- Apache Camel JMS Component -->
		<dependency>
			<groupId>org.apache.camel.springboot</groupId>
			<artifactId>camel-jms-starter</artifactId>
			<version>${camel.version}</version>
		</dependency>

		<!-- Apache Camel Mail Component -->
		<dependency>
			<groupId>org.apache.camel</groupId>
			<artifactId>camel-mail</artifactId>
			<version>${camel.version}</version>
		</dependency>


		<!-- Apache Camel FTP/FTPS Component -->
		<dependency>
			<groupId>org.apache.camel</groupId>
			<artifactId>camel-ftp</artifactId>
			<version>${camel.version}</version>
		</dependency>



		<dependency>
			<groupId>org.apache.camel</groupId>
			<artifactId>camel-servicenow</artifactId>
			<version>${camel.version}</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/lib/camel-servicenow-4.8.1.jar</systemPath>
		</dependency>


		<!-- TIBCO JMS dependency -->
		<dependency>
			<groupId>com.tibco</groupId>
			<artifactId>tibjms</artifactId>
			<version>10.3</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/lib/tibjms.jar</systemPath>
		</dependency>

		<dependency>
			<groupId>com.tibco</groupId>
			<artifactId>tibjmsadmin</artifactId>
			<version>10.3</version>
			<scope>system</scope>
			<systemPath>${project.basedir}/lib/tibjmsadmin.jar</systemPath>
		</dependency>

		<!-- JMS API dependency -->
		<dependency>
			<groupId>jakarta.jms</groupId>
			<artifactId>jakarta.jms-api</artifactId>
			<version>3.1.0</version>
		</dependency>

		<!-- Jakarta to javax JMS bridge for compatibility with older JMS clients -->
		<dependency>
			<groupId>javax.jms</groupId>
			<artifactId>javax.jms-api</artifactId>
			<version>2.0.1</version>
		</dependency>

		<!-- Camel Test dependencies -->
		<dependency>
			<groupId>org.apache.camel</groupId>
			<artifactId>camel-test-spring-junit5</artifactId>
			<version>${camel.version}</version>
			<scope>test</scope>
		</dependency>

		<!-- H2 Database -->
		<dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
			<scope>runtime</scope>
		</dependency>

		<!-- Apache Camel Spring Boot Starter -->
		<dependency>
			<groupId>org.apache.camel.springboot</groupId>
			<artifactId>camel-spring-boot-starter</artifactId>
			<version>${camel.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.camel.springboot</groupId>
			<artifactId>camel-jaxb-starter</artifactId>
			<version>${camel.version}</version>
		</dependency>
		<!-- Apache Camel Core -->
		<dependency>
			<groupId>org.apache.camel</groupId>
			<artifactId>camel-core</artifactId>
			<version>${camel.version}</version>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
